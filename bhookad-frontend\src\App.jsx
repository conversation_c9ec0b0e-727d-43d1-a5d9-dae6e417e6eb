import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate, Link } from 'react-router-dom'
import { AuthProvider } from './contexts/AuthContext'
import ErrorBoundary from './components/ui/ErrorBoundary'
// import { useLenis } from './hooks/useLenis'

// Landing Page Components
import Header from './components/Header'
import Hero from './components/Hero'
import HowItWorks from './components/HowItWorks'
import TrendingVendors from './components/TrendingVendors'
import SuccessStories from './components/SuccessStories'
import About from './components/About'
import Contact from './components/Contact'
import Footer from './components/Footer'

// Enhanced Sections
import VendorsSection from './components/VendorsSection'
import VloggersSection from './components/VloggersSection'
import ClickSpark from './components/ClickSpark'

// Authentication Components
import Login from './components/auth/Login'
import Register from './components/auth/Register'
import AuthCallback from './components/auth/AuthCallback'
import RoleSelection from './components/auth/RoleSelection'
import ProtectedRoute from './components/auth/ProtectedRoute'
import ProfileSetup from './pages/ProfileSetup'
import ProfileCompletionGuard from './components/auth/ProfileCompletionGuard'
// import RoleBasedRedirect from './components/auth/RoleBasedRedirect'

// Dashboard Components
import AdminDashboard from './components/dashboards/AdminDashboard'

// Profile Components
import VendorProfile from './components/profiles/VendorProfile'
import VloggerProfile from './components/profiles/VloggerProfile'

// Promotion Wall Component
import PromotionWall from './components/PromotionWall'

// Social Bite Components
import BiteHome from './pages/BiteHome'
import BiteCreate from './pages/BiteCreate'
import BiteSearch from './pages/BiteSearch'
import UserProfile from './pages/UserProfile'
import ModernUserProfile from './pages/ModernUserProfile'
import SubscriptionPlans from './pages/SubscriptionPlans'
import BiteActivity from './pages/BiteActivity'
import VendorDashboard from './pages/VendorDashboardSimple'
import UserDashboard from './pages/UserDashboard'
import VloggerDashboardNew from './pages/VloggerDashboard'
import BiteMessages from './pages/BiteMessages'



import './App.css'
import './styles/polish.css'
import './utils/smoothScroll.js'
import './styles/mobile.css'
import './styles/animations.css'

// Ultra Simple Test Component
const PromotionWallTest = () => {
  return (
    <div style={{ padding: '100px 20px', textAlign: 'center', background: '#FF6B35', color: 'white', minHeight: '100vh' }}>
      <h1>🎯 Promotion Wall Working!</h1>
      <p>Route connected successfully!</p>
      <Link to="/" style={{ color: 'white', textDecoration: 'underline' }}>Back to Home</Link>
    </div>
  )
}

// Quick CTA Component
const QuickCTA = () => (
  <section style={{
    background: 'linear-gradient(135deg, #ff6b35, #f7931e)',
    padding: '80px 0',
    textAlign: 'center',
    color: 'white'
  }}>
    <div className="container">
      <h2 style={{ fontSize: '2.5rem', fontWeight: '700', marginBottom: '20px' }}>
        Ready to Join the Food Revolution?
      </h2>
      <p style={{ fontSize: '1.2rem', marginBottom: '40px', opacity: '0.9' }}>
        Whether you're a vendor looking to grow or a vlogger ready to earn - we've got you covered!
      </p>
      <div style={{ display: 'flex', gap: '20px', justifyContent: 'center', flexWrap: 'wrap' }}>
        <a href="/test-vendor" style={{
          background: 'white',
          color: '#ff6b35',
          padding: '15px 30px',
          borderRadius: '8px',
          textDecoration: 'none',
          fontWeight: '600',
          transition: 'all 0.3s ease'
        }}>
          Start as Vendor
        </a>
        <a href="/test-vlogger" style={{
          background: 'rgba(255,255,255,0.2)',
          color: 'white',
          padding: '15px 30px',
          borderRadius: '8px',
          textDecoration: 'none',
          fontWeight: '600',
          border: '2px solid white',
          transition: 'all 0.3s ease'
        }}>
          Join as Vlogger
        </a>
      </div>
    </div>
  </section>
)

// Landing Page Component
const LandingPage = () => {
  return (
    <div className="landing-page">
      <Header />
      <Hero />
      <HowItWorks />
      <TrendingVendors />
      <SuccessStories />
      <VendorsSection />
      <VloggersSection />
      <QuickCTA />
      <About />
      <Contact />
      <Footer />
    </div>
  )
}

function App() {
  // Initialize smooth scrolling
  // useLenis()

  return (
    <ErrorBoundary>
      <AuthProvider>
        <Router>
          <ProfileCompletionGuard>
            <Routes>
          {/* Landing Page Route */}
          <Route path="/" element={<LandingPage />} />

          {/* Authentication Routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/auth/callback" element={<AuthCallback />} />

          {/* Profile Setup Route */}
          <Route path="/profile-setup" element={<ProfileSetup />} />

          {/* Social Bite Routes */}
          <Route path="/bite" element={<BiteHome />} />
          <Route path="/bite/create" element={<BiteCreate />} />
          <Route path="/bite/search" element={<BiteSearch />} />
          <Route path="/bite/activity" element={<BiteActivity />} />
          <Route path="/bite/profile/:userId" element={<ModernUserProfile />} />
          <Route path="/bite/subscription" element={<SubscriptionPlans />} />
          <Route path="/bite/vendor-dashboard" element={<VendorDashboard />} />
          <Route path="/bite/user-dashboard" element={<UserDashboard />} />
          <Route path="/bite/vlogger-dashboard" element={<VloggerDashboardNew />} />
          <Route path="/bite/messages" element={<BiteMessages />} />
          <Route path="/test-bite" element={<BiteHome />} /> {/* Direct test route */}

          {/* Promotion Wall Route */}
          <Route path="/promotion-wall" element={<PromotionWall />} />



          {/* Testing Routes - Direct access without auth */}
          <Route path="/test-vlogger" element={<VloggerDashboardNew />} />
          <Route path="/test-vendor" element={<VendorDashboard />} />
          <Route path="/test-admin" element={<AdminDashboard />} />

          {/* Profile Routes */}
          <Route path="/vendor/:id" element={<VendorProfile />} />
          <Route path="/vlogger/:id" element={<VloggerProfile />} />
          <Route path="/test-vendor-profile" element={<VendorProfile />} />
          <Route path="/test-vlogger-profile" element={<VloggerProfile />} />
          <Route path="/auth/callback" element={<AuthCallback />} />

          {/* Protected Routes */}
          <Route
            path="/select-role"
            element={
              <ProtectedRoute>
                <RoleSelection />
              </ProtectedRoute>
            }
          />
          <Route
            path="/vendor/dashboard"
            element={
              <ProtectedRoute>
                <VendorDashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="/vlogger/dashboard"
            element={
              <ProtectedRoute>
                <VloggerDashboardNew />
              </ProtectedRoute>
            }
          />
            <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </ProfileCompletionGuard>
        </Router>
        <ClickSpark />
      </AuthProvider>
    </ErrorBoundary>
  )
}

export default App

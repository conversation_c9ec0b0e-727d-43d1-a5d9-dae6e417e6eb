import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Home, Search, Plus, Heart, User, Bell, MessageCircle, TrendingUp, Star, Calendar } from 'lucide-react'
import Dock from '../ui/Dock'

import { useAuth } from '../../contexts/AuthContext'
import { useNotifications } from '../../hooks/useNotifications'
import './BiteLayoutNew.css'

const BiteLayout = ({ children, currentPage = 'home' }) => {
  const navigate = useNavigate()
  const { user } = useAuth()
  const { unreadCount } = useNotifications()

  // Mock data for top vendors
  const topVendors = [
    { id: 1, name: '<PERSON>', rating: 4.8, image: 'https://via.placeholder.com/40', specialty: 'Street Food' },
    { id: 2, name: 'Delhi Momos Corner', rating: 4.7, image: 'https://via.placeholder.com/40', specialty: 'Momos' },
    { id: 3, name: 'Punjabi Dhaba', rating: 4.9, image: 'https://via.placeholder.com/40', specialty: 'North Indian' },
    { id: 4, name: 'South Indian Express', rating: 4.6, image: 'https://via.placeholder.com/40', specialty: 'South Indian' }
  ]

  // Mock data for top vloggers
  const topVloggers = [
    { id: 1, name: 'Food Explorer', followers: '12.5K', image: 'https://via.placeholder.com/40', verified: true },
    { id: 2, name: 'Delhi Foodie', followers: '8.2K', image: 'https://via.placeholder.com/40', verified: true },
    { id: 3, name: 'Street Food Hunter', followers: '15.1K', image: 'https://via.placeholder.com/40', verified: false },
    { id: 4, name: 'Taste Tester', followers: '6.8K', image: 'https://via.placeholder.com/40', verified: true }
  ]

  // Trending hashtags
  const trendingHashtags = [
    { tag: '#streetfood', posts: '2.1K' },
    { tag: '#momos', posts: '1.8K' },
    { tag: '#chaat', posts: '1.5K' },
    { tag: '#delhi', posts: '3.2K' },
    { tag: '#foodie', posts: '4.1K' }
  ]

  // Dock navigation items
  const dockItems = [
    {
      icon: <Home size={20} />,
      label: 'Home',
      onClick: () => navigate('/bite'),
      active: currentPage === 'home'
    },
    {
      icon: <Search size={20} />,
      label: 'Search',
      onClick: () => navigate('/bite/search'),
      active: currentPage === 'search'
    },
    {
      icon: <Plus size={20} />,
      label: 'Create',
      onClick: () => navigate('/bite/create'),
      active: currentPage === 'create'
    },
    {
      icon: <Heart size={20} />,
      label: 'Activity',
      onClick: () => navigate('/bite/activity'),
      active: currentPage === 'activity'
    },
    {
      icon: <User size={20} />,
      label: 'Profile',
      onClick: () => navigate(`/bite/profile/${user?.id}`),
      active: currentPage === 'profile'
    }
  ]

  return (
    <div className="bite-layout">
      {/* Top Header */}
      <header className="bite-header">
        <div className="bite-header-content">
          {/* Logo Section */}
          <div className="bite-logo" onClick={() => navigate('/')}>
            <div className="logo-icon">🍛</div>
            <div className="logo-text">
              <h1 className="brand-name">Bhookad</h1>
              <span className="bite-tagline">Bite</span>
            </div>
          </div>

          {/* Search Bar */}
          <div className="bite-search-bar">
            <input
              type="text"
              placeholder="Search food, vendors, vloggers..."
              className="search-input"
            />
            <button className="search-btn">
              <Search size={18} />
            </button>
          </div>

          {/* Header Actions */}
          <div className="bite-header-actions">
            <button
              className="bite-header-btn"
              onClick={() => navigate('/bite/messages')}
              title="Messages"
            >
              <MessageCircle size={20} />
            </button>
            <button
              className="bite-header-btn notification-btn"
              onClick={() => navigate('/bite/activity')}
              title="Notifications"
            >
              <Bell size={20} />
              {unreadCount > 0 && (
                <span className="notification-badge">{unreadCount}</span>
              )}
            </button>

          </div>
        </div>
      </header>

      {/* Main Content - 3 Column Layout */}
      <main className="bite-main">
        {/* Left Sidebar */}
        <aside className="bite-left-sidebar">
          {/* Top Vendors */}
          <div className="sidebar-section">
            <h3 className="sidebar-title">
              <Star className="sidebar-icon" size={18} />
              Top Vendors
            </h3>
            <div className="vendor-list">
              {topVendors.map(vendor => (
                <div key={vendor.id} className="vendor-item">
                  <img src={vendor.image} alt={vendor.name} className="vendor-avatar" />
                  <div className="vendor-info">
                    <h4 className="vendor-name">{vendor.name}</h4>
                    <p className="vendor-specialty">{vendor.specialty}</p>
                    <div className="vendor-rating">
                      <Star size={12} fill="#FF6B35" color="#FF6B35" />
                      <span>{vendor.rating}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Top Vloggers */}
          <div className="sidebar-section">
            <h3 className="sidebar-title">
              <TrendingUp className="sidebar-icon" size={18} />
              Top Vloggers
            </h3>
            <div className="vlogger-list">
              {topVloggers.map(vlogger => (
                <div key={vlogger.id} className="vlogger-item">
                  <img src={vlogger.image} alt={vlogger.name} className="vlogger-avatar" />
                  <div className="vlogger-info">
                    <h4 className="vlogger-name">
                      {vlogger.name}
                      {vlogger.verified && <span className="verified-badge">✓</span>}
                    </h4>
                    <p className="vlogger-followers">{vlogger.followers} followers</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </aside>

        {/* Center Feed */}
        <div className="bite-center-feed">
          {children}
        </div>

        {/* Right Sidebar */}
        <aside className="bite-right-sidebar">
          {/* Trending Hashtags */}
          <div className="sidebar-section">
            <h3 className="sidebar-title">
              <TrendingUp className="sidebar-icon" size={18} />
              Trending Hashtags
            </h3>
            <div className="hashtag-list">
              {trendingHashtags.map((item, index) => (
                <div key={index} className="hashtag-item">
                  <span className="hashtag">{item.tag}</span>
                  <span className="hashtag-count">{item.posts} posts</span>
                </div>
              ))}
            </div>
          </div>

          {/* Upcoming Events */}
          <div className="sidebar-section">
            <h3 className="sidebar-title">
              <Calendar className="sidebar-icon" size={18} />
              Food Events
            </h3>
            <div className="events-list">
              <div className="event-item">
                <div className="event-date">
                  <span className="event-day">25</span>
                  <span className="event-month">Jul</span>
                </div>
                <div className="event-info">
                  <h4 className="event-name">Street Food Festival</h4>
                  <p className="event-location">CP, Delhi</p>
                </div>
              </div>
              <div className="event-item">
                <div className="event-date">
                  <span className="event-day">28</span>
                  <span className="event-month">Jul</span>
                </div>
                <div className="event-info">
                  <h4 className="event-name">Food Truck Rally</h4>
                  <p className="event-location">Gurgaon</p>
                </div>
              </div>
            </div>
          </div>
        </aside>
      </main>

      {/* Bottom Dock Navigation */}
      <div className="bite-dock-container">
        <Dock
          items={dockItems}
          panelHeight={68}
          baseItemSize={50}
          magnification={70}
          spring={{ mass: 0.03, stiffness: 400, damping: 18 }} // Super fast animation
          distance={120} // Quick response distance
        />
      </div>
    </div>
  )
}

export default BiteLayout

/* Modern User Profile Full-Screen Styles */
.modern-user-profile-fullscreen {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B35, #f7931e);
  padding: 0;
  margin: 0;
  width: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.profile-header-simple {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.back-btn-simple {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: white;
  cursor: pointer;
  font-weight: 500;
}

.profile-title {
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

/* Dock Navigation */
.dock-navigation {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  padding: 10px 20px;
  display: flex;
  gap: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.dock-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  padding: 8px 12px;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
  font-size: 0.7rem;
  font-weight: 500;
}

.dock-btn:hover {
  background: rgba(255, 107, 53, 0.1);
  color: #ff6b35;
  transform: translateY(-2px);
}

.dock-btn.active {
  background: rgba(255, 107, 53, 0.2);
  color: #ff6b35;
}

.dock-btn.create-btn {
  background: #ff6b35;
  color: white;
}

.dock-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

/* Feed Header Styles - Same as BiteLayout */
.bite-header {
  background: white;
  border-bottom: 1px solid #dbdbdb;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: 60px;
  min-height: 60px;
  max-height: 60px;
  overflow: hidden;
}

.bite-header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.75rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1.5rem;
  height: 60px;
  box-sizing: border-box;
  overflow: hidden;
}

/* Logo Section */
.bite-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  min-width: fit-content;
}

.bite-logo:hover {
  transform: scale(1.02);
}

.logo-icon {
  font-size: 2rem;
  filter: drop-shadow(0 2px 4px rgba(255, 107, 53, 0.3));
}

.logo-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.brand-name {
  font-size: 1.5rem;
  font-weight: 800;
  color: #FF6B35;
  margin: 0;
  line-height: 1;
}

.bite-tagline {
  font-size: 0.75rem;
  color: #8e8e8e;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 0.1rem;
}

/* Search Bar */
.bite-search-bar {
  flex: 0 1 350px;
  max-width: 350px;
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  padding-right: 3rem;
  border: 1px solid #dbdbdb;
  border-radius: 25px;
  background: #f5f5f5;
  font-size: 0.9rem;
  outline: none;
  transition: all 0.2s ease;
}

.search-input:focus {
  background: white;
  border-color: #FF6B35;
  box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.1);
}

.search-input::placeholder {
  color: #8e8e8e;
}

.search-btn {
  position: absolute;
  right: 0.5rem;
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 50%;
  cursor: pointer;
  color: #8e8e8e;
  transition: all 0.2s ease;
}

.search-btn:hover {
  background: #FF6B35;
  color: white;
}

.bite-header-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-shrink: 0;
  min-width: fit-content;
}

.bite-header-btn {
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 50%;
  cursor: pointer;
  color: #8e8e8e;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.bite-header-btn:hover {
  background: #f5f5f5;
  color: #FF6B35;
}

/* Instagram-Style Profile Design */
.modern-user-profile-fullscreen {
  min-height: 100vh;
  background: #fafafa;
  padding-bottom: 100px; /* Space for dock */
}

/* Profile Picture - Perfect Circle */
.w-24 { width: 6rem; height: 6rem; }
.h-24 { height: 6rem; }
.md\:w-36 { width: 9rem; }
.md\:h-36 { height: 9rem; }
.rounded-full {
  border-radius: 50% !important;
}
.object-cover {
  object-fit: cover;
}
.border-4 {
  border-width: 4px;
}
.border-orange-500 {
  border-color: #f97316;
}

/* Override any conflicting styles */
.w-1\/3 { width: 33.333333%; }
.w-2\/3 { width: 66.666667%; }
.aspect-square { aspect-ratio: 1 / 1; }

/* Responsive Grid */
.grid { display: grid; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.gap-1 { gap: 0.25rem; }
.gap-4 { gap: 1rem; }

/* Hover Effects */
.group:hover .group-hover\:opacity-100 { opacity: 1; }
.opacity-0 { opacity: 0; }
.transition-opacity { transition: opacity 0.3s ease; }

/* Text Colors */
.text-zinc-800 { color: #27272a; }
.text-zinc-600 { color: #52525b; }
.text-zinc-500 { color: #71717a; }
.text-zinc-200 { color: #e4e4e7; }
.bg-zinc-200 { background-color: #e4e4e7; }
.bg-zinc-100 { background-color: #f4f4f5; }
.border-zinc-200 { border-color: #e4e4e7; }

/* Spacing */
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.gap-8 { gap: 2rem; }
.p-4 { padding: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }

/* Borders */
.border-t { border-top-width: 1px; }
.border-t-2 { border-top-width: 2px; }
.border-y { border-top-width: 1px; border-bottom-width: 1px; }

/* Flexbox */
.flex { display: flex; }
.flex-1 { flex: 1 1 0%; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-start { justify-content: flex-start; }
.justify-around { justify-content: space-around; }
.flex-shrink-0 { flex-shrink: 0; }

/* Text */
.text-center { text-align: center; }
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.uppercase { text-transform: uppercase; }
.tracking-wider { letter-spacing: 0.05em; }

/* Responsive */
@media (min-width: 768px) {
  .md\:w-36 { width: 9rem; }
  .md\:h-36 { height: 9rem; }
  .md\:flex { display: flex; }
  .md\:hidden { display: none; }
  .md\:space-y-4 > * + * { margin-top: 1rem; }
  .md\:flex-none { flex: none; }
  .md\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .md\:inline { display: inline; }
  .md\:gap-4 { gap: 1rem; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:p-4 { padding: 1rem; }
  .md\:col-span-3 { grid-column: span 3 / span 3; }
}

/* Hide on mobile */
@media (max-width: 767px) {
  .md\:hidden { display: none !important; }
  .hidden { display: none; }
}

/* Original Dock Navigation - Keep as is */

/* Custom Profile Navigation Header */
.profile-nav-header {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 107, 53, 0.2);
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
}

.nav-left, .nav-right {
  flex: 1;
}

.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.back-btn-nav {
  background: none;
  border: none;
  color: #FF6B35;
  font-weight: 600;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.back-btn-nav:hover {
  background: rgba(255, 107, 53, 0.1);
}

.bhookad-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  color: #FF6B35;
}

.logo-icon {
  font-size: 1.5rem;
}

.logo-text {
  font-size: 1.25rem;
}

.nav-right {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.nav-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.nav-btn:hover {
  background: rgba(255, 107, 53, 0.1);
}

.modern-user-profile {
  min-height: calc(100vh - 80px);
  background: transparent;
  padding: 0;
  margin: 0;
}

/* Orange Food Background Header */
.profile-header-modern {
  height: 200px;
  background: linear-gradient(135deg, #FF6B35, #f7931e);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.food-graphics {
  display: flex;
  gap: 3rem;
  align-items: center;
  opacity: 0.3;
}

.food-item {
  font-size: 4rem;
  animation: float 3s ease-in-out infinite;
}

.food-item.burger { animation-delay: 0s; }
.food-item.taco { animation-delay: 0.5s; }
.food-item.soup { animation-delay: 1s; }
.food-item.pizza { animation-delay: 1.5s; }

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Main Content Layout */
.profile-content-modern {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 2rem;
  max-width: 1200px;
  margin: -100px auto 0;
  padding: 0 2rem 2rem;
  position: relative;
  z-index: 2;
}

/* Left Column - Profile Card */
.profile-left {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.profile-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

/* Avatar Section */
.profile-avatar-section {
  padding: 2rem;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.avatar-container {
  position: relative;
  display: inline-block;
  margin-bottom: 1rem;
}

.profile-avatar-modern {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.verification-badge {
  position: absolute;
  bottom: -5px;
  right: -5px;
}

.role-badge-modern {
  background: #FF6B35;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  white-space: nowrap;
}

.profile-info-modern {
  text-align: center;
}

.profile-name-modern {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.profile-bio-modern {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}

/* Stats Section */
.profile-stats-modern {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item-modern {
  text-align: center;
  flex: 1;
}

.stat-number-modern {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #FF6B35;
}

.stat-label-modern {
  font-size: 0.8rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-divider {
  width: 1px;
  height: 30px;
  background: #e5e7eb;
  margin: 0 1rem;
}

/* Meta Information */
.profile-meta-modern {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #f0f0f0;
}

.meta-item-modern {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  color: #666;
  font-size: 0.9rem;
}

.meta-item-modern:last-child {
  margin-bottom: 0;
}

.meta-item-modern svg {
  color: #FF6B35;
}

/* Food Preferences */
.food-preferences-modern {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #f0f0f0;
}

.food-preferences-modern h4 {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
}

.preferences-tags-modern {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.preference-tag-modern {
  background: rgba(255, 107, 53, 0.1);
  color: #FF6B35;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Action Buttons */
.profile-actions-modern {
  padding: 1.5rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.action-btn-modern {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.action-btn-modern.primary {
  background: #FF6B35;
  color: white;
}

.action-btn-modern.primary:hover {
  background: #e55a2b;
  transform: translateY(-1px);
}

.action-btn-modern.secondary {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #e5e7eb;
}

.action-btn-modern.secondary:hover {
  background: #e5e7eb;
}

/* Right Column - Content */
.profile-right {
  min-height: 600px;
}

.content-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

/* Content Tabs */
.profile-tabs-modern {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  background: #f8f9fa;
}

.tab-btn-modern {
  flex: 1;
  padding: 1rem;
  background: none;
  border: none;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.tab-btn-modern:hover {
  color: #FF6B35;
  background: rgba(255, 107, 53, 0.05);
}

.tab-btn-modern.active {
  color: #FF6B35;
  background: white;
}

.tab-btn-modern.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #FF6B35;
}

/* Content Area */
.profile-content-area {
  padding: 2rem;
  min-height: 400px;
}

/* Posts Grid */
.posts-grid-modern {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.profile-post-modern {
  aspect-ratio: 1;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.profile-post-modern:hover {
  transform: scale(1.02);
}

.profile-post-modern img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.post-overlay-modern {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.profile-post-modern:hover .post-overlay-modern {
  opacity: 1;
}

.post-stats-modern {
  display: flex;
  gap: 1rem;
  color: white;
  font-weight: 600;
}

.post-stats-modern span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* No Posts State */
.no-posts-modern {
  text-align: center;
  padding: 3rem 1rem;
}

.no-posts-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.no-posts-modern h3 {
  color: #333;
  margin: 0 0 0.5rem 0;
}

.no-posts-modern p {
  color: #666;
  margin: 0 0 1.5rem 0;
}

.create-first-post-modern {
  background: #FF6B35;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
}

.create-first-post-modern:hover {
  background: #e55a2b;
}

/* Coming Soon States */
.coming-soon {
  text-align: center;
  padding: 3rem 1rem;
}

.coming-soon-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.coming-soon h3 {
  color: #333;
  margin: 0 0 0.5rem 0;
}

.coming-soon p {
  color: #666;
  margin: 0;
}

/* Loading States */
.profile-loading-modern {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: white;
}

.loading-spinner-modern {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.profile-error-modern {
  text-align: center;
  padding: 3rem 1rem;
  color: white;
}

.back-btn-modern {
  background: white;
  color: #FF6B35;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 1rem;
}

/* Bottom Dock Navigation */
.dock-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 107, 53, 0.2);
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 1000;
}

.dock-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 12px;
  transition: all 0.2s ease;
  min-width: 60px;
}

.dock-btn:hover {
  color: #FF6B35;
  background: rgba(255, 107, 53, 0.1);
}

.dock-btn.active {
  color: #FF6B35;
  background: rgba(255, 107, 53, 0.1);
}

.dock-btn.create-btn {
  background: #FF6B35;
  color: white;
}

.dock-btn.create-btn:hover {
  background: #e55a2b;
  transform: scale(1.05);
}

.dock-btn span {
  font-size: 0.7rem;
  font-weight: 500;
}

.dock-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

/* Add bottom padding to content to avoid dock overlap */
.modern-user-profile {
  padding-bottom: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-content-modern {
    grid-template-columns: 1fr;
    margin-top: -50px;
    padding: 0 1rem 1rem;
  }

  .profile-left {
    position: static;
  }

  .food-graphics {
    gap: 1rem;
  }

  .food-item {
    font-size: 2.5rem;
  }

  .profile-nav-header {
    padding: 0.75rem 1rem;
  }

  .dock-navigation {
    padding: 0.5rem;
  }

  .dock-btn {
    min-width: 50px;
    padding: 0.25rem;
  }

  .dock-btn span {
    font-size: 0.6rem;
  }
}

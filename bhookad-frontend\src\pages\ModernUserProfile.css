/* Modern User Profile Full-Screen Styles */
.modern-user-profile-fullscreen {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B35, #f7931e);
  padding: 0;
  margin: 0;
  width: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.profile-header-simple {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.back-btn-simple {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: white;
  cursor: pointer;
  font-weight: 500;
}

.profile-title {
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

/* Dock Navigation */
.dock-navigation {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  padding: 10px 20px;
  display: flex;
  gap: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.dock-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  padding: 8px 12px;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
  font-size: 0.7rem;
  font-weight: 500;
}

.dock-btn:hover {
  background: rgba(255, 107, 53, 0.1);
  color: #ff6b35;
  transform: translateY(-2px);
}

.dock-btn.active {
  background: rgba(255, 107, 53, 0.2);
  color: #ff6b35;
}

.dock-btn.create-btn {
  background: #ff6b35;
  color: white;
}

.dock-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

/* Original Dock Navigation - Keep as is */

/* Custom Profile Navigation Header */
.profile-nav-header {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 107, 53, 0.2);
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
}

.nav-left, .nav-right {
  flex: 1;
}

.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.back-btn-nav {
  background: none;
  border: none;
  color: #FF6B35;
  font-weight: 600;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.back-btn-nav:hover {
  background: rgba(255, 107, 53, 0.1);
}

.bhookad-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  color: #FF6B35;
}

.logo-icon {
  font-size: 1.5rem;
}

.logo-text {
  font-size: 1.25rem;
}

.nav-right {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.nav-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.nav-btn:hover {
  background: rgba(255, 107, 53, 0.1);
}

.modern-user-profile {
  min-height: calc(100vh - 80px);
  background: transparent;
  padding: 0;
  margin: 0;
}

/* Orange Food Background Header */
.profile-header-modern {
  height: 200px;
  background: linear-gradient(135deg, #FF6B35, #f7931e);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.food-graphics {
  display: flex;
  gap: 3rem;
  align-items: center;
  opacity: 0.3;
}

.food-item {
  font-size: 4rem;
  animation: float 3s ease-in-out infinite;
}

.food-item.burger { animation-delay: 0s; }
.food-item.taco { animation-delay: 0.5s; }
.food-item.soup { animation-delay: 1s; }
.food-item.pizza { animation-delay: 1.5s; }

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Main Content Layout */
.profile-content-modern {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 2rem;
  max-width: 1200px;
  margin: -100px auto 0;
  padding: 0 2rem 2rem;
  position: relative;
  z-index: 2;
}

/* Left Column - Profile Card */
.profile-left {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.profile-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

/* Avatar Section */
.profile-avatar-section {
  padding: 2rem;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.avatar-container {
  position: relative;
  display: inline-block;
  margin-bottom: 1rem;
}

.profile-avatar-modern {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.verification-badge {
  position: absolute;
  bottom: -5px;
  right: -5px;
}

.role-badge-modern {
  background: #FF6B35;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  white-space: nowrap;
}

.profile-info-modern {
  text-align: center;
}

.profile-name-modern {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.profile-bio-modern {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}

/* Stats Section */
.profile-stats-modern {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item-modern {
  text-align: center;
  flex: 1;
}

.stat-number-modern {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #FF6B35;
}

.stat-label-modern {
  font-size: 0.8rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-divider {
  width: 1px;
  height: 30px;
  background: #e5e7eb;
  margin: 0 1rem;
}

/* Meta Information */
.profile-meta-modern {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #f0f0f0;
}

.meta-item-modern {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  color: #666;
  font-size: 0.9rem;
}

.meta-item-modern:last-child {
  margin-bottom: 0;
}

.meta-item-modern svg {
  color: #FF6B35;
}

/* Food Preferences */
.food-preferences-modern {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #f0f0f0;
}

.food-preferences-modern h4 {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
}

.preferences-tags-modern {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.preference-tag-modern {
  background: rgba(255, 107, 53, 0.1);
  color: #FF6B35;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Action Buttons */
.profile-actions-modern {
  padding: 1.5rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.action-btn-modern {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.action-btn-modern.primary {
  background: #FF6B35;
  color: white;
}

.action-btn-modern.primary:hover {
  background: #e55a2b;
  transform: translateY(-1px);
}

.action-btn-modern.secondary {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #e5e7eb;
}

.action-btn-modern.secondary:hover {
  background: #e5e7eb;
}

/* Right Column - Content */
.profile-right {
  min-height: 600px;
}

.content-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

/* Content Tabs */
.profile-tabs-modern {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  background: #f8f9fa;
}

.tab-btn-modern {
  flex: 1;
  padding: 1rem;
  background: none;
  border: none;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.tab-btn-modern:hover {
  color: #FF6B35;
  background: rgba(255, 107, 53, 0.05);
}

.tab-btn-modern.active {
  color: #FF6B35;
  background: white;
}

.tab-btn-modern.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #FF6B35;
}

/* Content Area */
.profile-content-area {
  padding: 2rem;
  min-height: 400px;
}

/* Posts Grid */
.posts-grid-modern {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.profile-post-modern {
  aspect-ratio: 1;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.profile-post-modern:hover {
  transform: scale(1.02);
}

.profile-post-modern img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.post-overlay-modern {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.profile-post-modern:hover .post-overlay-modern {
  opacity: 1;
}

.post-stats-modern {
  display: flex;
  gap: 1rem;
  color: white;
  font-weight: 600;
}

.post-stats-modern span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* No Posts State */
.no-posts-modern {
  text-align: center;
  padding: 3rem 1rem;
}

.no-posts-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.no-posts-modern h3 {
  color: #333;
  margin: 0 0 0.5rem 0;
}

.no-posts-modern p {
  color: #666;
  margin: 0 0 1.5rem 0;
}

.create-first-post-modern {
  background: #FF6B35;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
}

.create-first-post-modern:hover {
  background: #e55a2b;
}

/* Coming Soon States */
.coming-soon {
  text-align: center;
  padding: 3rem 1rem;
}

.coming-soon-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.coming-soon h3 {
  color: #333;
  margin: 0 0 0.5rem 0;
}

.coming-soon p {
  color: #666;
  margin: 0;
}

/* Loading States */
.profile-loading-modern {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: white;
}

.loading-spinner-modern {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.profile-error-modern {
  text-align: center;
  padding: 3rem 1rem;
  color: white;
}

.back-btn-modern {
  background: white;
  color: #FF6B35;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 1rem;
}

/* Bottom Dock Navigation */
.dock-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 107, 53, 0.2);
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 1000;
}

.dock-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 12px;
  transition: all 0.2s ease;
  min-width: 60px;
}

.dock-btn:hover {
  color: #FF6B35;
  background: rgba(255, 107, 53, 0.1);
}

.dock-btn.active {
  color: #FF6B35;
  background: rgba(255, 107, 53, 0.1);
}

.dock-btn.create-btn {
  background: #FF6B35;
  color: white;
}

.dock-btn.create-btn:hover {
  background: #e55a2b;
  transform: scale(1.05);
}

.dock-btn span {
  font-size: 0.7rem;
  font-weight: 500;
}

.dock-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

/* Add bottom padding to content to avoid dock overlap */
.modern-user-profile {
  padding-bottom: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-content-modern {
    grid-template-columns: 1fr;
    margin-top: -50px;
    padding: 0 1rem 1rem;
  }

  .profile-left {
    position: static;
  }

  .food-graphics {
    gap: 1rem;
  }

  .food-item {
    font-size: 2.5rem;
  }

  .profile-nav-header {
    padding: 0.75rem 1rem;
  }

  .dock-navigation {
    padding: 0.5rem;
  }

  .dock-btn {
    min-width: 50px;
    padding: 0.25rem;
  }

  .dock-btn span {
    font-size: 0.6rem;
  }
}

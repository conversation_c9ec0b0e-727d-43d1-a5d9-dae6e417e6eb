/* Profile Dropdown Styles */
.profile-dropdown {
  position: relative;
  display: inline-block !important;
  z-index: 10;
  width: 40px;
  height: 40px;
  overflow: visible;
  opacity: 1 !important;
  visibility: visible !important;
}

.profile-trigger {
  display: flex !important;
  align-items: center;
  justify-content: center;
  background: rgba(245, 245, 245, 0.5);
  border: 1px solid #dbdbdb;
  cursor: pointer;
  padding: 6px;
  border-radius: 50%;
  transition: all 0.2s ease;
  width: 40px;
  height: 40px;
  position: relative;
  z-index: 10;
  box-sizing: border-box;
  opacity: 1 !important;
  visibility: visible !important;
}

.profile-trigger:hover {
  background: rgba(255, 107, 53, 0.1);
  border-color: #FF6B35;
}

.profile-avatar {
  width: 26px !important;
  height: 26px !important;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #FF6B35;
  transition: all 0.2s ease;
  display: block !important;
  margin: 0;
  opacity: 1 !important;
  visibility: visible !important;
}

.profile-avatar-placeholder {
  width: 26px !important;
  height: 26px !important;
  border-radius: 50%;
  background: #FF6B35 !important;
  color: white !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  border: 2px solid #FF6B35;
  opacity: 1 !important;
  visibility: visible !important;
}

.profile-trigger:hover .profile-avatar {
  border-color: #FF6B35;
}

.profile-indicator {
  position: absolute;
  bottom: -1px;
  right: -1px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.role-badge {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.5rem;
  border: 1px solid white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Dropdown Menu */
.dropdown-menu {
  position: absolute !important;
  top: calc(100% + 0.5rem) !important;
  right: 0 !important;
  background: white !important;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #dbdbdb;
  min-width: 240px;
  z-index: 99999 !important;
  animation: dropdownFadeIn 0.2s ease-out;
  overflow: hidden;
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Dropdown Header */
.dropdown-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(255, 107, 53, 0.05);
  border-bottom: 1px solid #e5e7eb;
}

.dropdown-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dropdown-user-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

.user-role {
  font-size: 0.8rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Dropdown Items */
.dropdown-items {
  padding: 0.5rem 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #333;
  font-size: 0.9rem;
}

.dropdown-item:hover {
  background: rgba(255, 107, 53, 0.1);
  color: #FF6B35;
}

.dropdown-item svg {
  flex-shrink: 0;
  color: #666;
}

.dropdown-item:hover svg {
  color: #FF6B35;
}

.dropdown-item span {
  font-weight: 500;
}

/* Logout Item */
.dropdown-item.logout {
  color: #ef4444;
  border-top: 1px solid #e5e7eb;
  margin-top: 0.5rem;
}

.dropdown-item.logout:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.dropdown-item.logout svg {
  color: #ef4444;
}

.dropdown-item.logout:hover svg {
  color: #dc2626;
}

/* Dropdown Divider */
.dropdown-divider {
  height: 1px;
  background: #e5e7eb;
  margin: 0.5rem 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .profile-name {
    display: none;
  }
  
  .dropdown-menu {
    right: -10px;
    min-width: 200px;
  }
  
  .dropdown-header {
    padding: 0.75rem;
  }
  
  .dropdown-item {
    padding: 0.6rem 0.75rem;
  }
}

/* Animation */
.dropdown-menu {
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

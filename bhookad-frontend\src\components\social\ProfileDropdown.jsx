import React, { useState, useRef, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { User, BarChart3, Settings, LogOut, Crown } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import './ProfileDropdown.css'

const ProfileDropdown = () => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef(null)
  const { user, userProfile, signOut } = useAuth()
  const navigate = useNavigate()

  const handleClickOutside = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setIsOpen(false)
    }
  }

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLogout = async () => {
    try {
      await signOut()
      navigate('/')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  const handleDashboard = () => {
    const role = userProfile?.role || 'user'
    if (role === 'vendor') {
      navigate('/bite/vendor-dashboard')
    } else if (role === 'vlogger') {
      navigate('/bite/vlogger-dashboard')
    } else {
      navigate('/bite/user-dashboard')
    }
    setIsOpen(false)
  }

  const handleProfile = () => {
    navigate(`/bite/profile/${user?.id}`)
    setIsOpen(false)
  }

  const handleSubscription = () => {
    navigate('/bite/subscription')
    setIsOpen(false)
  }

  const getRoleBadge = (role) => {
    const badges = {
      vendor: { icon: '🏪', label: 'Vendor', color: '#FF6B35' },
      vlogger: { icon: '🎥', label: 'Vlogger', color: '#f7931e' },
      user: { icon: '👤', label: 'User', color: '#6b7280' }
    }
    return badges[role] || badges.user
  }

  const roleBadge = getRoleBadge(userProfile?.role)

  if (!user) return null

  return (
    <div className="profile-dropdown" ref={dropdownRef}>
      <button
        className="profile-trigger"
        onClick={() => setIsOpen(!isOpen)}
      >
        <img
          src={userProfile?.avatar_url || 'https://via.placeholder.com/32'}
          alt={userProfile?.name || 'User'}
          className="profile-avatar"
        />
        <span className="profile-name">{userProfile?.name || 'User'}</span>
      </button>

      {isOpen && (
        <div className="dropdown-menu">
          {/* User Info */}
          <div className="dropdown-header">
            <img 
              src={userProfile?.avatar_url || 'https://via.placeholder.com/40'} 
              alt={userProfile?.name || 'User'}
              className="dropdown-avatar"
            />
            <div className="dropdown-user-info">
              <h4>{userProfile?.name || 'User'}</h4>
              <div className="user-role" style={{ color: roleBadge.color }}>
                {roleBadge.icon} {roleBadge.label}
              </div>
            </div>
          </div>

          <div className="dropdown-divider"></div>

          {/* Menu Items */}
          <div className="dropdown-items">
            <button className="dropdown-item" onClick={handleProfile}>
              <User size={18} />
              <span>My Profile</span>
            </button>

            <button className="dropdown-item" onClick={handleDashboard}>
              <BarChart3 size={18} />
              <span>Dashboard</span>
            </button>

            <button className="dropdown-item" onClick={handleSubscription}>
              <Crown size={18} />
              <span>Subscription</span>
            </button>

            <button className="dropdown-item">
              <Settings size={18} />
              <span>Settings</span>
            </button>
          </div>

          <div className="dropdown-divider"></div>

          {/* Logout */}
          <button className="dropdown-item logout" onClick={handleLogout}>
            <LogOut size={18} />
            <span>Logout</span>
          </button>
        </div>
      )}
    </div>
  )
}

export default ProfileDropdown

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://nzrwdumsbkyskcxhjlfz.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56cndkdW1zYmt5c2tjeGhqbGZ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU1MjE0MzksImV4cCI6MjA2MTA5NzQzOX0.Ux2QfVX7d3t3AsyHUSDoMFqcVoJRTud5rvSceMHFGag'

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

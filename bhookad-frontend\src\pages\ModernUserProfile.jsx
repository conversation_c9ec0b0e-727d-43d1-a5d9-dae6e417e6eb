import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { MapPin, Calendar, Heart, MessageCircle, Settings, BarChart3, Home, Search, Plus, Activity } from 'lucide-react'
import FollowButton from '../components/social/FollowButton'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { useFollow } from '../hooks/useFollow'
import './ModernUserProfile.css'

const ModernUserProfile = () => {
  const { userId } = useParams()
  const navigate = useNavigate()
  const { user: currentUser } = useAuth()
  
  // YOUR REAL DATA from database
  const [profile, setProfile] = useState({
    id: '12e60606-ac74-4602-a2f0-478934d1046b',
    email: '<EMAIL>',
    name: '<PERSON><PERSON><PERSON>',
    phone: '+91-9876543210',
    city: 'Delhi',
    role: 'user',
    bio: 'Food enthusiast exploring the best flavors in Delhi! 🍕🍜',
    avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200',
    food_preferences: ['Indian', 'Street Food', 'Continental'],
    profile_completed: true,
    created_at: '2025-07-20T15:09:53.95912+00:00'
  })
  const [posts, setPosts] = useState([
    {
      id: 'b968d1f0-f95d-4799-a518-035174123d27',
      user_id: '12e60606-ac74-4602-a2f0-478934d1046b',
      content: 'Amazing butter chicken at this local dhaba! 🍗',
      image_url: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
      location: 'Connaught Place, Delhi',
      likes_count: 45,
      comments_count: 12,
      created_at: '2025-07-20T15:10:14.913056+00:00'
    },
    {
      id: '3ad26cc8-b41d-4330-a558-bf6ee221f6a2',
      user_id: '12e60606-ac74-4602-a2f0-478934d1046b',
      content: 'Street food paradise! Best chaat in town 🌮',
      image_url: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400',
      location: 'Chandni Chowk, Delhi',
      likes_count: 32,
      comments_count: 8,
      created_at: '2025-07-20T15:10:14.913056+00:00'
    },
    {
      id: 'c6cef4d7-8ef8-4755-be1f-e3192b0ad24b',
      user_id: '12e60606-ac74-4602-a2f0-478934d1046b',
      content: 'Homemade pasta perfection 🍝',
      image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400',
      location: 'Home Kitchen',
      likes_count: 67,
      comments_count: 15,
      created_at: '2025-07-20T15:10:14.913056+00:00'
    }
  ])
  const [stats, setStats] = useState({ posts: 0, followers: 0, following: 0 })
  const [loading, setLoading] = useState(false) // Start with false to show profile immediately
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('posts')

  const { getFollowersCount, getFollowingCount } = useFollow()
  const isOwnProfile = currentUser?.id === userId

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric'
    })
  }

  const fetchProfile = async () => {
    try {
      const profileUserId = userId || currentUser?.id
      console.log('🔄 Fetching profile for:', profileUserId)

      if (!profileUserId) {
        console.log('❌ No user ID available')
        setLoading(false)
        return
      }

      // Try to fetch real profile first
      const { data: profileData, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', profileUserId)
        .single()

      console.log('📊 Profile response:', { profileData, profileError })

      if (profileData) {
        console.log('✅ Profile found:', profileData)
        setProfile(profileData)
      } else {
        console.log('❌ No profile found, creating mock profile')
        // Create mock profile if not found
        const mockProfile = {
          id: profileUserId,
          name: 'Tarun Soni',
          role: 'user',
          city: 'Delhi',
          bio: 'Food enthusiast exploring the best flavors in Delhi! 🍕🍜',
          avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200',
          created_at: new Date().toISOString(),
          food_preferences: ['Indian', 'Street Food', 'Continental']
        }
        setProfile(mockProfile)
      }

      // Fetch real user posts
      const { data: postsData } = await supabase
        .from('posts')
        .select('*')
        .eq('user_id', profileUserId)
        .order('created_at', { ascending: false })

      console.log('📝 Posts response:', postsData)

      if (postsData && postsData.length > 0) {
        // Use real posts data without fake counts
        setPosts(postsData)
      } else {
        setPosts([])
      }

      // Fetch real followers count (handle if follows table doesn't exist)
      let followersCount = 0
      let followingCount = 0

      try {
        const { count: realFollowersCount } = await supabase
          .from('follows')
          .select('*', { count: 'exact', head: true })
          .eq('following_id', profileUserId)

        const { count: realFollowingCount } = await supabase
          .from('follows')
          .select('*', { count: 'exact', head: true })
          .eq('follower_id', profileUserId)

        followersCount = realFollowersCount || 0
        followingCount = realFollowingCount || 0
      } catch (followError) {
        console.log('⚠️ Follows table not found, using 0 counts')
      }

      // Set real stats
      setStats({
        posts: postsData?.length || 0,
        followers: followersCount,
        following: followingCount
      })

      console.log('✅ Real stats set:', {
        posts: postsData?.length || 0,
        followers: followersCount,
        following: followingCount
      })

      console.log('✅ Profile fetch complete')

    } catch (error) {
      console.error('❌ Error fetching profile:', error)
      setError('Failed to load profile')
    } finally {
      console.log('✅ Setting loading to false')
      setLoading(false)
    }
  }

  const handleFollowChange = () => {
    fetchProfile() // Refresh to update follower count
  }

  useEffect(() => {
    if (userId || currentUser?.id) {
      fetchProfile()
    }
  }, [userId, currentUser])

  if (loading) {
    return (
      <div className="modern-user-profile-fullscreen">
        <div className="profile-loading-modern">
          <div className="loading-spinner-modern"></div>
          <p>Loading profile...</p>
        </div>
      </div>
    )
  }

  if (error || !profile) {
    return (
      <div className="modern-user-profile-fullscreen">
        <div className="profile-error-modern">
          <h2>Profile not found</h2>
          <p>{error || 'This user does not exist'}</p>
          <button onClick={() => navigate('/bite')} className="back-btn-modern">
            Back to Feed
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="modern-user-profile-fullscreen">
      {/* Original Simple Header */}
      <div className="profile-header-simple">
        <button
          onClick={() => navigate('/bite')}
          className="back-btn-simple"
        >
          ← Back to Feed
        </button>
        <h2 className="profile-title">Profile</h2>
        <div></div>
      </div>

      <div className="modern-user-profile">
        {/* Profile Card */}
        <div className="profile-card">
          <div className="profile-header-section">
            <div className="profile-avatar-container">
              <img
                src={profile?.avatar_url || 'https://via.placeholder.com/120'}
                alt={profile?.name}
                className="profile-avatar"
              />
            </div>
            <div className="profile-info">
              <h1 className="profile-name">{profile?.name || 'User'}</h1>
              <p className="profile-handle">@{profile?.email?.split('@')[0] || 'user'}</p>
              <p className="profile-bio">{profile?.bio || 'No bio available'}</p>

              <div className="profile-meta">
                <div className="meta-item">
                  <MapPin size={16} />
                  <span>{profile?.city || 'Location not set'}</span>
                </div>
                <div className="meta-item">
                  <Calendar size={16} />
                  <span>Joined {formatDate(profile?.created_at)}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="profile-stats">
            <div className="stat-item">
              <span className="stat-number">{stats.following}</span>
              <span className="stat-label">Following</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{stats.followers}</span>
              <span className="stat-label">followers</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{stats.posts}</span>
              <span className="stat-label">posts</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="profile-actions">
            {isOwnProfile ? (
              <>
                <button
                  className="action-btn edit-btn"
                  onClick={() => navigate('/profile-setup')}
                >
                  <Settings size={18} />
                  Edit Profile
                </button>
                <button
                  className="action-btn dashboard-btn"
                  onClick={() => {
                    switch (profile?.role) {
                      case 'vendor':
                        navigate('/vendor/dashboard')
                        break
                      case 'vlogger':
                        navigate('/vlogger/dashboard')
                        break
                      default:
                        navigate('/user/dashboard')
                    }
                  }}
                >
                  <BarChart3 size={18} />
                  Dashboard
                </button>
              </>
            ) : (
              <>
                <FollowButton
                  targetUserId={userId}
                  targetUserName={profile?.name}
                  size="medium"
                  variant="primary"
                  onFollowChange={handleFollowChange}
                />
                <button
                  className="action-btn message-btn"
                  onClick={() => navigate('/bite/messages')}
                >
                  <MessageCircle size={18} />
                  Message
                </button>
              </>
            )}
          </div>
        </div>

        {/* Content Tabs */}
        <div className="profile-tabs">
          <button
            className={`tab-btn ${activeTab === 'posts' ? 'active' : ''}`}
            onClick={() => setActiveTab('posts')}
          >
            Posts ({stats.posts})
          </button>
          <button
            className={`tab-btn ${activeTab === 'liked' ? 'active' : ''}`}
            onClick={() => setActiveTab('liked')}
          >
            Liked
          </button>
        </div>

        {/* Content Area */}
        <div className="profile-content">
          {activeTab === 'posts' && (
            <div className="posts-grid">
              {posts.length > 0 ? (
                posts.map(post => (
                  <div key={post.id} className="post-item">
                    <img src={post.image_url} alt="Post" />
                    <div className="post-overlay">
                      <div className="post-stats">
                        <span><Heart size={16} /> {post.likes_count}</span>
                        <span><MessageCircle size={16} /> {post.comments_count}</span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="no-posts">
                  <div className="no-posts-icon">📸</div>
                  <h3>No posts yet</h3>
                  <p>Start sharing your food adventures!</p>
                  {isOwnProfile && (
                    <button
                      onClick={() => navigate('/bite/create')}
                      className="create-first-post"
                    >
                      Create your first post
                    </button>
                  )}
                </div>
              )}
            </div>
          )}

          {activeTab === 'liked' && (
            <div className="liked-posts">
              <div className="coming-soon">
                <div className="coming-soon-icon">❤️</div>
                <h3>Liked Posts</h3>
                <p>Your liked posts will appear here</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Bottom Dock Navigation */}
      <div className="dock-navigation">
        <button
          className="dock-btn"
          onClick={() => navigate('/bite')}
        >
          🏠
          <span>Home</span>
        </button>
        <button
          className="dock-btn"
          onClick={() => navigate('/bite/search')}
        >
          🔍
          <span>Search</span>
        </button>
        <button
          className="dock-btn create-btn"
          onClick={() => navigate('/bite/create')}
        >
          ➕
          <span>Create</span>
        </button>
        <button
          className="dock-btn"
          onClick={() => navigate('/bite/activity')}
        >
          🔔
          <span>Activity</span>
        </button>
        <button
          className="dock-btn active"
          onClick={() => navigate(`/bite/profile/${currentUser?.id}`)}
        >
          <img
            src={currentUser?.avatar_url || profile?.avatar_url || 'https://via.placeholder.com/24'}
            alt="Profile"
            className="dock-avatar"
          />
          <span>Profile</span>
        </button>
      </div>
    </div>
  )
}

export default ModernUserProfile

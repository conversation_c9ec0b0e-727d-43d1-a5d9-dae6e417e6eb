import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { MapPin, Calendar, Heart, MessageCircle, Settings, BarChart3, Home, Search, Plus, Activity } from 'lucide-react'
import FollowButton from '../components/social/FollowButton'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { useFollow } from '../hooks/useFollow'
import './ModernUserProfile.css'

const ModernUserProfile = () => {
  const { userId } = useParams()
  const navigate = useNavigate()
  const { user: currentUser } = useAuth()
  
  // Start with default profile to avoid loading screen
  const [profile, setProfile] = useState({
    id: currentUser?.id || 'test-123',
    name: 'Tarun Soni',
    role: 'user',
    city: 'Delhi',
    bio: 'Food enthusiast exploring the best flavors in Delhi! 🍕🍜',
    avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200',
    created_at: new Date().toISOString(),
    food_preferences: ['Indian', 'Street Food', 'Continental']
  })
  const [posts, setPosts] = useState([
    {
      id: 1,
      image_url: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
      likes_count: 45,
      comments_count: 12
    },
    {
      id: 2,
      image_url: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400',
      likes_count: 32,
      comments_count: 8
    },
    {
      id: 3,
      image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400',
      likes_count: 67,
      comments_count: 15
    }
  ])
  const [stats, setStats] = useState({ posts: 3, followers: 1250, following: 340 })
  const [loading, setLoading] = useState(false) // Start with false to show profile immediately
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('posts')

  const { getFollowersCount, getFollowingCount } = useFollow()
  const isOwnProfile = currentUser?.id === userId

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric'
    })
  }

  const fetchProfile = async () => {
    try {
      const profileUserId = userId || currentUser?.id
      console.log('🔄 Fetching profile for:', profileUserId)

      if (!profileUserId) {
        console.log('❌ No user ID available')
        setLoading(false)
        return
      }

      // Try to fetch real profile first
      const { data: profileData, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', profileUserId)
        .single()

      console.log('📊 Profile response:', { profileData, profileError })

      if (profileData) {
        console.log('✅ Profile found:', profileData)
        setProfile(profileData)
      } else {
        console.log('❌ No profile found, creating mock profile')
        // Create mock profile if not found
        const mockProfile = {
          id: profileUserId,
          name: 'Tarun Soni',
          role: 'user',
          city: 'Delhi',
          bio: 'Food enthusiast exploring the best flavors in Delhi! 🍕🍜',
          avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200',
          created_at: new Date().toISOString(),
          food_preferences: ['Indian', 'Street Food', 'Continental']
        }
        setProfile(mockProfile)
      }

      // Fetch real user posts
      const { data: postsData } = await supabase
        .from('posts')
        .select('*')
        .eq('user_id', profileUserId)
        .order('created_at', { ascending: false })

      console.log('📝 Posts response:', postsData)

      if (postsData && postsData.length > 0) {
        const postsWithCounts = postsData.map(post => ({
          ...post,
          likes_count: Math.floor(Math.random() * 50) + 10,
          comments_count: Math.floor(Math.random() * 20) + 5
        }))
        setPosts(postsWithCounts)
      } else {
        setPosts([])
      }

      // Set mock stats for now (skip follower API calls that might be slow)
      setStats({
        posts: postsData?.length || 0,
        followers: 1250,
        following: 340
      })

      console.log('✅ Profile fetch complete')

    } catch (error) {
      console.error('❌ Error fetching profile:', error)
      setError('Failed to load profile')
    } finally {
      console.log('✅ Setting loading to false')
      setLoading(false)
    }
  }

  const handleFollowChange = () => {
    fetchProfile() // Refresh to update follower count
  }

  useEffect(() => {
    if (userId || currentUser?.id) {
      fetchProfile()
    }
  }, [userId, currentUser])

  if (loading) {
    return (
      <div className="modern-user-profile-fullscreen">
        <div className="profile-loading-modern">
          <div className="loading-spinner-modern"></div>
          <p>Loading profile...</p>
        </div>
      </div>
    )
  }

  if (error || !profile) {
    return (
      <div className="modern-user-profile-fullscreen">
        <div className="profile-error-modern">
          <h2>Profile not found</h2>
          <p>{error || 'This user does not exist'}</p>
          <button onClick={() => navigate('/bite')} className="back-btn-modern">
            Back to Feed
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="modern-user-profile-fullscreen">
      {/* Simple Header */}
      <div className="profile-nav-header">
        <button
          onClick={() => navigate('/bite')}
          className="back-btn-nav"
        >
          ← Back to Feed
        </button>
        <div className="bhookad-logo">
          <span className="logo-text">Bhookad</span>
        </div>
        <div></div>
      </div>

      <div className="modern-user-profile">
        {/* Orange Food Background Header */}
        <div className="profile-header-modern">
          <div className="food-graphics">
            <div className="food-item burger">🍔</div>
            <div className="food-item taco">🌮</div>
            <div className="food-item soup">🍜</div>
            <div className="food-item pizza">🍕</div>
          </div>
        </div>

        {/* Main Profile Content */}
        <div className="profile-content-modern">
          {/* Left Column - Profile Info */}
          <div className="profile-left">
            <div className="profile-card">
              {/* Avatar and Basic Info */}
              <div className="profile-avatar-section">
                <div className="avatar-container">
                  <img
                    src={profile.avatar_url || 'https://via.placeholder.com/80'}
                    alt={profile.name}
                    className="profile-avatar-modern"
                  />
                  <div className="verification-badge">
                    <span className="role-badge-modern">
                      👤 Food Explorer
                    </span>
                  </div>
                </div>

                <div className="profile-info-modern">
                  <h1 className="profile-name-modern">{profile.name}</h1>
                  <p className="profile-bio-modern">
                    {profile.bio || 'Food enthusiast exploring the best flavors! 🍕🍜'}
                  </p>
                </div>
              </div>

              {/* Stats */}
              <div className="profile-stats-modern">
                <div className="stat-item-modern">
                  <span className="stat-number-modern">{stats.posts}</span>
                  <span className="stat-label-modern">Posts</span>
                </div>
                <div className="stat-divider"></div>
                <div className="stat-item-modern">
                  <span className="stat-number-modern">{stats.followers}</span>
                  <span className="stat-label-modern">Followers</span>
                </div>
                <div className="stat-divider"></div>
                <div className="stat-item-modern">
                  <span className="stat-number-modern">{stats.following}</span>
                  <span className="stat-label-modern">Following</span>
                </div>
              </div>

              {/* Location and Join Date */}
              <div className="profile-meta-modern">
                {profile.city && (
                  <div className="meta-item-modern">
                    <MapPin size={16} />
                    <span>{profile.city}</span>
                  </div>
                )}
                <div className="meta-item-modern">
                  <Calendar size={16} />
                  <span>Joined {formatDate(profile.created_at)}</span>
                </div>
              </div>

              {/* Food Preferences */}
              {profile.food_preferences && profile.food_preferences.length > 0 && (
                <div className="food-preferences-modern">
                  <h4>Food Interests</h4>
                  <div className="preferences-tags-modern">
                    {profile.food_preferences.map((pref, index) => (
                      <span key={index} className="preference-tag-modern">
                        {pref}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="profile-actions-modern">
                {isOwnProfile ? (
                  <>
                    <button className="action-btn-modern primary">
                      <Settings size={18} />
                      Edit Profile
                    </button>
                    <button className="action-btn-modern secondary">
                      <BarChart3 size={18} />
                      Dashboard
                    </button>
                  </>
                ) : (
                  <>
                    <FollowButton
                      targetUserId={userId}
                      targetUserName={profile?.name}
                      size="medium"
                      variant="primary"
                      onFollowChange={handleFollowChange}
                    />
                    <button className="action-btn-modern secondary">
                      <MessageCircle size={18} />
                      Message
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Right Column - Content */}
          <div className="profile-right">
            <div className="content-card">
              {/* Content Tabs */}
              <div className="profile-tabs-modern">
                <button 
                  className={`tab-btn-modern ${activeTab === 'posts' ? 'active' : ''}`}
                  onClick={() => setActiveTab('posts')}
                >
                  Posts
                </button>
                <button 
                  className={`tab-btn-modern ${activeTab === 'liked' ? 'active' : ''}`}
                  onClick={() => setActiveTab('liked')}
                >
                  Liked
                </button>
                <button 
                  className={`tab-btn-modern ${activeTab === 'reviews' ? 'active' : ''}`}
                  onClick={() => setActiveTab('reviews')}
                >
                  Reviews
                </button>
              </div>

              {/* Content Area */}
              <div className="profile-content-area">
                {activeTab === 'posts' && (
                  <div className="posts-grid-modern">
                    {posts.length > 0 ? (
                      posts.map(post => (
                        <div key={post.id} className="profile-post-modern">
                          <img src={post.image_url} alt="Post" />
                          <div className="post-overlay-modern">
                            <div className="post-stats-modern">
                              <span><Heart size={16} /> {post.likes_count}</span>
                              <span><MessageCircle size={16} /> {post.comments_count}</span>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="no-posts-modern">
                        <div className="no-posts-icon">📸</div>
                        <h3>No posts yet</h3>
                        <p>Start sharing your food adventures!</p>
                        {isOwnProfile && (
                          <button 
                            onClick={() => navigate('/bite/create')}
                            className="create-first-post-modern"
                          >
                            Create your first post
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'liked' && (
                  <div className="liked-posts-modern">
                    <div className="coming-soon">
                      <div className="coming-soon-icon">❤️</div>
                      <h3>Liked Posts</h3>
                      <p>Your liked posts will appear here</p>
                    </div>
                  </div>
                )}

                {activeTab === 'reviews' && (
                  <div className="reviews-modern">
                    <div className="coming-soon">
                      <div className="coming-soon-icon">⭐</div>
                      <h3>Reviews</h3>
                      <p>Your restaurant reviews will appear here</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Dock Navigation */}
      <div className="dock-navigation">
        <button
          className="dock-btn"
          onClick={() => navigate('/bite')}
        >
          🏠
          <span>Home</span>
        </button>
        <button
          className="dock-btn"
          onClick={() => navigate('/bite/search')}
        >
          🔍
          <span>Search</span>
        </button>
        <button
          className="dock-btn create-btn"
          onClick={() => navigate('/bite/create')}
        >
          ➕
          <span>Create</span>
        </button>
        <button
          className="dock-btn"
          onClick={() => navigate('/bite/activity')}
        >
          🔔
          <span>Activity</span>
        </button>
        <button
          className="dock-btn active"
          onClick={() => navigate(`/bite/profile/${currentUser?.id}`)}
        >
          <img
            src={currentUser?.avatar_url || profile?.avatar_url || 'https://via.placeholder.com/24'}
            alt="Profile"
            className="dock-avatar"
          />
          <span>Profile</span>
        </button>
      </div>
    </div>
  )
}

export default ModernUserProfile

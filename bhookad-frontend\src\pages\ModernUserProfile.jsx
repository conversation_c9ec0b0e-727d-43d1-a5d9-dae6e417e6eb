import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { MapPin, Calendar, Heart, MessageCircle, Settings, BarChart3, Search, Bell, User, Home, Plus, Activity } from 'lucide-react'
import FollowButton from '../components/social/FollowButton'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { useFollow } from '../hooks/useFollow'
import './ModernUserProfile.css'

const ModernUserProfile = () => {
  const { userId } = useParams()
  const navigate = useNavigate()
  const { user: currentUser } = useAuth()
  
  // YOUR REAL DATA from database
  const [profile, setProfile] = useState({
    id: '12e60606-ac74-4602-a2f0-478934d1046b',
    email: '<EMAIL>',
    name: '<PERSON><PERSON>',
    phone: '+91-9876543210',
    city: 'Delhi',
    role: 'user',
    bio: 'Food enthusiast exploring the best flavors in Delhi! 🍕🍜',
    avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200',
    food_preferences: ['Indian', 'Street Food', 'Continental'],
    profile_completed: true,
    created_at: '2025-07-20T15:09:53.95912+00:00'
  })
  const [posts, setPosts] = useState([
    {
      id: 'b968d1f0-f95d-4799-a518-035174123d27',
      user_id: '12e60606-ac74-4602-a2f0-478934d1046b',
      content: 'Amazing butter chicken at this local dhaba! 🍗',
      image_url: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
      location: 'Connaught Place, Delhi',
      likes_count: 45,
      comments_count: 12,
      created_at: '2025-07-20T15:10:14.913056+00:00'
    },
    {
      id: '3ad26cc8-b41d-4330-a558-bf6ee221f6a2',
      user_id: '12e60606-ac74-4602-a2f0-478934d1046b',
      content: 'Street food paradise! Best chaat in town 🌮',
      image_url: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400',
      location: 'Chandni Chowk, Delhi',
      likes_count: 32,
      comments_count: 8,
      created_at: '2025-07-20T15:10:14.913056+00:00'
    },
    {
      id: 'c6cef4d7-8ef8-4755-be1f-e3192b0ad24b',
      user_id: '12e60606-ac74-4602-a2f0-478934d1046b',
      content: 'Homemade pasta perfection 🍝',
      image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400',
      location: 'Home Kitchen',
      likes_count: 67,
      comments_count: 15,
      created_at: '2025-07-20T15:10:14.913056+00:00'
    }
  ])
  const [stats, setStats] = useState({ posts: 0, followers: 0, following: 0 })
  const [loading, setLoading] = useState(false) // Start with false to show profile immediately
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('posts')

  const { getFollowersCount, getFollowingCount } = useFollow()
  const isOwnProfile = currentUser?.id === userId

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric'
    })
  }

  const fetchProfile = async () => {
    try {
      const profileUserId = userId || currentUser?.id
      console.log('🔄 Fetching profile for:', profileUserId)

      if (!profileUserId) {
        console.log('❌ No user ID available')
        setLoading(false)
        return
      }

      // Try to fetch real profile first
      const { data: profileData, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', profileUserId)
        .single()

      console.log('📊 Profile response:', { profileData, profileError })

      if (profileData) {
        console.log('✅ Profile found:', profileData)
        setProfile(profileData)
      } else {
        console.log('❌ No profile found, creating mock profile')
        // Create mock profile if not found
        const mockProfile = {
          id: profileUserId,
          name: 'Tarun Soni',
          role: 'user',
          city: 'Delhi',
          bio: 'Food enthusiast exploring the best flavors in Delhi! 🍕🍜',
          avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200',
          created_at: new Date().toISOString(),
          food_preferences: ['Indian', 'Street Food', 'Continental']
        }
        setProfile(mockProfile)
      }

      // Fetch real user posts
      const { data: postsData } = await supabase
        .from('posts')
        .select('*')
        .eq('user_id', profileUserId)
        .order('created_at', { ascending: false })

      console.log('📝 Posts response:', postsData)

      if (postsData && postsData.length > 0) {
        // Use real posts data without fake counts
        setPosts(postsData)
      } else {
        setPosts([])
      }

      // Fetch real followers count (handle if follows table doesn't exist)
      let followersCount = 0
      let followingCount = 0

      try {
        const { count: realFollowersCount } = await supabase
          .from('follows')
          .select('*', { count: 'exact', head: true })
          .eq('following_id', profileUserId)

        const { count: realFollowingCount } = await supabase
          .from('follows')
          .select('*', { count: 'exact', head: true })
          .eq('follower_id', profileUserId)

        followersCount = realFollowersCount || 0
        followingCount = realFollowingCount || 0
      } catch (followError) {
        console.log('⚠️ Follows table not found, using 0 counts')
      }

      // Set real stats
      setStats({
        posts: postsData?.length || 0,
        followers: followersCount,
        following: followingCount
      })

      console.log('✅ Real stats set:', {
        posts: postsData?.length || 0,
        followers: followersCount,
        following: followingCount
      })

      console.log('✅ Profile fetch complete')

    } catch (error) {
      console.error('❌ Error fetching profile:', error)
      setError('Failed to load profile')
    } finally {
      console.log('✅ Setting loading to false')
      setLoading(false)
    }
  }

  const handleFollowChange = () => {
    fetchProfile() // Refresh to update follower count
  }

  useEffect(() => {
    if (userId || currentUser?.id) {
      fetchProfile()
    }
  }, [userId, currentUser])

  if (loading) {
    return (
      <div className="modern-user-profile-fullscreen">
        <div className="profile-loading-modern">
          <div className="loading-spinner-modern"></div>
          <p>Loading profile...</p>
        </div>
      </div>
    )
  }

  if (error || !profile) {
    return (
      <div className="modern-user-profile-fullscreen">
        <div className="profile-error-modern">
          <h2>Profile not found</h2>
          <p>{error || 'This user does not exist'}</p>
          <button onClick={() => navigate('/bite')} className="back-btn-modern">
            Back to Feed
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="modern-user-profile-fullscreen">
      {/* Feed Header - Same as BiteLayout */}
      <header className="bite-header">
        <div className="bite-header-content">
          {/* Logo Section */}
          <div className="bite-logo" onClick={() => navigate('/')}>
            <div className="logo-icon">🍛</div>
            <div className="logo-text">
              <h1 className="brand-name">Bhookad</h1>
              <span className="bite-tagline">Bite</span>
            </div>
          </div>

          {/* Search Bar */}
          <div className="bite-search-bar">
            <input
              type="text"
              placeholder="Search food, vendors, vloggers..."
              className="search-input"
            />
            <button className="search-btn">
              <Search size={18} />
            </button>
          </div>

          {/* Header Actions */}
          <div className="bite-header-actions">
            <button className="bite-header-btn">
              <Heart size={20} />
            </button>
            <button className="bite-header-btn">
              <Bell size={20} />
            </button>
            <button className="bite-header-btn">
              <User size={20} />
            </button>
          </div>
        </div>
      </header>

      {/* Instagram-Style Profile Layout */}
      <div className="w-full max-w-4xl mx-auto">
        {/* Profile Header */}
        <header className="flex items-center p-4">
          <div className="w-1/3 flex justify-center">
            <img
              className="w-24 h-24 md:w-36 md:h-36 rounded-full object-cover border-4 border-orange-500"
              src={profile?.avatar_url || 'https://via.placeholder.com/150'}
              alt="User Profile"
              style={{ borderRadius: '50%' }}
            />
          </div>
          <div className="w-2/3 space-y-3 md:space-y-4">
            <div className="flex items-center gap-4">
              <h2 className="text-2xl text-zinc-800">@{profile?.email?.split('@')[0] || 'user'}</h2>
              {isOwnProfile ? (
                <>
                  <button
                    className="font-semibold text-sm bg-zinc-200 px-4 py-1.5 rounded-lg hover:bg-zinc-300"
                    onClick={() => navigate('/profile-setup')}
                  >
                    Edit Profile
                  </button>
                  <button className="p-2 rounded-lg hover:bg-zinc-200">
                    <Settings className="w-5 h-5"/>
                  </button>
                </>
              ) : (
                <>
                  <FollowButton
                    targetUserId={userId}
                    targetUserName={profile?.name}
                    size="medium"
                    variant="primary"
                    onFollowChange={handleFollowChange}
                  />
                  <button
                    className="p-2 rounded-lg hover:bg-zinc-200"
                    onClick={() => navigate('/bite/messages')}
                  >
                    <MessageCircle className="w-5 h-5"/>
                  </button>
                </>
              )}
            </div>
            <div className="hidden md:flex justify-start gap-8">
              <div className="text-center">
                <p className="font-bold text-lg text-zinc-800">{stats.posts}</p>
                <p className="text-sm text-zinc-500">Posts</p>
              </div>
              <div className="text-center">
                <p className="font-bold text-lg text-zinc-800">{stats.followers}</p>
                <p className="text-sm text-zinc-500">Followers</p>
              </div>
              <div className="text-center">
                <p className="font-bold text-lg text-zinc-800">{stats.following}</p>
                <p className="text-sm text-zinc-500">Following</p>
              </div>
            </div>
            <div>
              <h1 className="font-bold text-zinc-900">{profile?.name || 'User'}</h1>
              <p className="text-zinc-600 text-sm">
                {profile?.bio || 'Street food enthusiast! Exploring new flavors daily! 🌮🍕'}
              </p>
              {profile?.city && (
                <p className="text-zinc-500 text-xs mt-1">
                  📍 {profile.city}
                </p>
              )}
            </div>
          </div>
        </header>

        {/* Mobile Stats */}
        <div className="md:hidden flex justify-around py-2 border-y border-zinc-200">
          <div className="text-center">
            <p className="font-bold text-lg text-zinc-800">{stats.posts}</p>
            <p className="text-sm text-zinc-500">Posts</p>
          </div>
          <div className="text-center">
            <p className="font-bold text-lg text-zinc-800">{stats.followers}</p>
            <p className="text-sm text-zinc-500">Followers</p>
          </div>
          <div className="text-center">
            <p className="font-bold text-lg text-zinc-800">{stats.following}</p>
            <p className="text-sm text-zinc-500">Following</p>
          </div>
        </div>

        {/* Profile Highlights - Food Cuisines */}
        <div className="flex items-center gap-4 p-4 overflow-x-auto">
          {['Indian', 'Chinese', 'Italian', 'Sweets', 'Spicy'].map((cuisine, i) => (
            <div key={`${cuisine}-${i}`} className="text-center flex-shrink-0">
              <button className="w-16 h-16 bg-zinc-100 rounded-full flex items-center justify-center ring-2 ring-zinc-300 hover:ring-orange-400 transition">
                <span className="text-2xl">
                  {cuisine === 'Indian' ? '🍛' :
                   cuisine === 'Chinese' ? '🥡' :
                   cuisine === 'Italian' ? '🍝' :
                   cuisine === 'Sweets' ? '🍰' : '🌶️'}
                </span>
              </button>
              <p className="text-xs mt-2 font-medium">{cuisine}</p>
            </div>
          ))}
        </div>

        {/* Tabs Section */}
        <div className="border-t border-zinc-200">
          <div className="flex justify-center">
            <button
              onClick={() => setActiveTab('posts')}
              className={`flex-1 md:flex-none md:px-8 py-3 flex items-center justify-center gap-2 text-sm font-semibold transition-colors uppercase tracking-wider ${
                activeTab === 'posts' ? 'text-zinc-800 border-t-2 border-zinc-800' : 'text-zinc-500 hover:text-zinc-800'
              }`}
            >
              <MessageCircle className="w-5 h-5"/>
              <span className="hidden md:inline">POSTS</span>
            </button>
            <button
              onClick={() => setActiveTab('reviews')}
              className={`flex-1 md:flex-none md:px-8 py-3 flex items-center justify-center gap-2 text-sm font-semibold transition-colors uppercase tracking-wider ${
                activeTab === 'reviews' ? 'text-zinc-800 border-t-2 border-zinc-800' : 'text-zinc-500 hover:text-zinc-800'
              }`}
            >
              <Settings className="w-5 h-5"/>
              <span className="hidden md:inline">REVIEWS</span>
            </button>
            <button
              onClick={() => setActiveTab('bookmarks')}
              className={`flex-1 md:flex-none md:px-8 py-3 flex items-center justify-center gap-2 text-sm font-semibold transition-colors uppercase tracking-wider ${
                activeTab === 'bookmarks' ? 'text-zinc-800 border-t-2 border-zinc-800' : 'text-zinc-500 hover:text-zinc-800'
              }`}
            >
              <Heart className="w-5 h-5"/>
              <span className="hidden md:inline">BOOKMARKS</span>
            </button>
          </div>

          {/* Tab Content */}
          <div className="p-1 md:p-4">
            {activeTab === 'posts' && (
              <div className="grid grid-cols-3 gap-1 md:gap-4">
                {posts.length > 0 ? (
                  posts.map(post => (
                    <div key={post.id} className="relative bg-zinc-200 group aspect-square">
                      <img
                        className="w-full h-full object-cover"
                        src={post.image_url || 'https://via.placeholder.com/400'}
                        alt="User post"
                      />
                      <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="flex items-center space-x-4 text-white font-bold">
                          <span className="flex items-center">
                            <Heart className="w-5 h-5 mr-1" fill="white"/>
                            {post.likes_count || 0}
                          </span>
                          <span className="flex items-center">
                            <MessageCircle className="w-5 h-5 mr-1" fill="white"/>
                            {post.comments_count || 0}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-span-3 text-center py-12">
                    <div className="text-6xl mb-4">📸</div>
                    <h3 className="text-xl font-bold text-zinc-800 mb-2">No posts yet</h3>
                    <p className="text-zinc-600 mb-4">Start sharing your food adventures!</p>
                    {isOwnProfile && (
                      <button
                        onClick={() => navigate('/bite/create')}
                        className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition"
                      >
                        Create your first post
                      </button>
                    )}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'reviews' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">⭐</div>
                  <h3 className="text-xl font-bold text-zinc-800 mb-2">No reviews yet</h3>
                  <p className="text-zinc-600">Your restaurant reviews will appear here</p>
                </div>
              </div>
            )}

            {activeTab === 'bookmarks' && (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="col-span-2 md:col-span-3 text-center py-12">
                  <div className="text-6xl mb-4">🔖</div>
                  <h3 className="text-xl font-bold text-zinc-800 mb-2">No bookmarks yet</h3>
                  <p className="text-zinc-600">Your saved places will appear here</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Bottom Dock Navigation - Original */}
      <div className="dock-navigation">
        <button
          className="dock-btn"
          onClick={() => navigate('/bite')}
        >
          <Home size={20} />
          <span>Home</span>
        </button>
        <button
          className="dock-btn"
          onClick={() => navigate('/bite/search')}
        >
          <Search size={20} />
          <span>Search</span>
        </button>
        <button
          className="dock-btn create-btn"
          onClick={() => navigate('/bite/create')}
        >
          <Plus size={20} />
          <span>Create</span>
        </button>
        <button
          className="dock-btn"
          onClick={() => navigate('/bite/activity')}
        >
          <Activity size={20} />
          <span>Activity</span>
        </button>
        <button
          className="dock-btn active"
          onClick={() => navigate(`/bite/profile/${currentUser?.id}`)}
        >
          <User size={20} />
          <span>Profile</span>
        </button>
      </div>
    </div>
  )
}

export default ModernUserProfile

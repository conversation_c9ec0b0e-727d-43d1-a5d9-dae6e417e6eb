import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { MapPin, Calendar, Heart, MessageCircle, Settings, BarChart3, Home, Search, Plus, User } from 'lucide-react'
import FollowButton from '../components/social/FollowButton'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import Dock from '../components/ui/Dock'
import { useFollow } from '../hooks/useFollow'
import './ModernUserProfile.css'

const ModernUserProfile = () => {
  const { userId } = useParams()
  const navigate = useNavigate()
  const { user: currentUser } = useAuth()
  
  // YOUR REAL DATA from database
  const [profile, setProfile] = useState({
    id: '12e60606-ac74-4602-a2f0-478934d1046b',
    email: '<EMAIL>',
    name: '<PERSON><PERSON><PERSON>',
    phone: '+91-9876543210',
    city: 'Delhi',
    role: 'user',
    bio: 'Food enthusiast exploring the best flavors in Delhi! 🍕🍜',
    avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200',
    food_preferences: ['Indian', 'Street Food', 'Continental'],
    profile_completed: true,
    created_at: '2025-07-20T15:09:53.95912+00:00'
  })
  const [posts, setPosts] = useState([
    {
      id: 'b968d1f0-f95d-4799-a518-035174123d27',
      user_id: '12e60606-ac74-4602-a2f0-478934d1046b',
      content: 'Amazing butter chicken at this local dhaba! 🍗',
      image_url: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
      location: 'Connaught Place, Delhi',
      likes_count: 45,
      comments_count: 12,
      created_at: '2025-07-20T15:10:14.913056+00:00'
    },
    {
      id: '3ad26cc8-b41d-4330-a558-bf6ee221f6a2',
      user_id: '12e60606-ac74-4602-a2f0-478934d1046b',
      content: 'Street food paradise! Best chaat in town 🌮',
      image_url: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400',
      location: 'Chandni Chowk, Delhi',
      likes_count: 32,
      comments_count: 8,
      created_at: '2025-07-20T15:10:14.913056+00:00'
    },
    {
      id: 'c6cef4d7-8ef8-4755-be1f-e3192b0ad24b',
      user_id: '12e60606-ac74-4602-a2f0-478934d1046b',
      content: 'Homemade pasta perfection 🍝',
      image_url: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=400',
      location: 'Home Kitchen',
      likes_count: 67,
      comments_count: 15,
      created_at: '2025-07-20T15:10:14.913056+00:00'
    }
  ])
  const [stats, setStats] = useState({ posts: 0, followers: 0, following: 0 })
  const [loading, setLoading] = useState(false) // Start with false to show profile immediately
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('posts')

  const { getFollowersCount, getFollowingCount } = useFollow()
  const isOwnProfile = currentUser?.id === userId

  // Dock navigation items (same as feed)
  const dockItems = [
    {
      icon: <Home size={20} />,
      label: 'Home',
      onClick: () => navigate('/bite'),
      active: false
    },
    {
      icon: <Search size={20} />,
      label: 'Search',
      onClick: () => navigate('/bite/search'),
      active: false
    },
    {
      icon: <Plus size={20} />,
      label: 'Create',
      onClick: () => navigate('/bite/create'),
      active: false
    },
    {
      icon: <Heart size={20} />,
      label: 'Activity',
      onClick: () => navigate('/bite/activity'),
      active: false
    },
    {
      icon: <User size={20} />,
      label: 'Profile',
      onClick: () => navigate(`/bite/profile/${currentUser?.id}`),
      active: true
    }
  ]

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric'
    })
  }

  const fetchProfile = async () => {
    try {
      const profileUserId = userId || currentUser?.id
      console.log('🔄 Fetching profile for:', profileUserId)

      if (!profileUserId) {
        console.log('❌ No user ID available')
        setLoading(false)
        return
      }

      // Try to fetch real profile first
      const { data: profileData, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', profileUserId)
        .single()

      console.log('📊 Profile response:', { profileData, profileError })

      if (profileData) {
        console.log('✅ Profile found:', profileData)
        setProfile(profileData)
      } else {
        console.log('❌ No profile found, creating mock profile')
        // Create mock profile if not found
        const mockProfile = {
          id: profileUserId,
          name: 'Tarun Soni',
          role: 'user',
          city: 'Delhi',
          bio: 'Food enthusiast exploring the best flavors in Delhi! 🍕🍜',
          avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200',
          created_at: new Date().toISOString(),
          food_preferences: ['Indian', 'Street Food', 'Continental']
        }
        setProfile(mockProfile)
      }

      // Fetch real user posts
      const { data: postsData } = await supabase
        .from('posts')
        .select('*')
        .eq('user_id', profileUserId)
        .order('created_at', { ascending: false })

      console.log('📝 Posts response:', postsData)

      if (postsData && postsData.length > 0) {
        // Use real posts data without fake counts
        setPosts(postsData)
      } else {
        setPosts([])
      }

      // Fetch real followers count (handle if follows table doesn't exist)
      let followersCount = 0
      let followingCount = 0

      try {
        const { count: realFollowersCount } = await supabase
          .from('follows')
          .select('*', { count: 'exact', head: true })
          .eq('following_id', profileUserId)

        const { count: realFollowingCount } = await supabase
          .from('follows')
          .select('*', { count: 'exact', head: true })
          .eq('follower_id', profileUserId)

        followersCount = realFollowersCount || 0
        followingCount = realFollowingCount || 0
      } catch (followError) {
        console.log('⚠️ Follows table not found, using 0 counts')
      }

      // Set real stats
      setStats({
        posts: postsData?.length || 0,
        followers: followersCount,
        following: followingCount
      })

      console.log('✅ Real stats set:', {
        posts: postsData?.length || 0,
        followers: followersCount,
        following: followingCount
      })

      console.log('✅ Profile fetch complete')

    } catch (error) {
      console.error('❌ Error fetching profile:', error)
      setError('Failed to load profile')
    } finally {
      console.log('✅ Setting loading to false')
      setLoading(false)
    }
  }

  const handleFollowChange = () => {
    fetchProfile() // Refresh to update follower count
  }

  useEffect(() => {
    if (userId || currentUser?.id) {
      fetchProfile()
    }
  }, [userId, currentUser])

  if (loading) {
    return (
      <div className="modern-user-profile-fullscreen">
        <div className="profile-loading-modern">
          <div className="loading-spinner-modern"></div>
          <p>Loading profile...</p>
        </div>
      </div>
    )
  }

  if (error || !profile) {
    return (
      <div className="modern-user-profile-fullscreen">
        <div className="profile-error-modern">
          <h2>Profile not found</h2>
          <p>{error || 'This user does not exist'}</p>
          <button onClick={() => navigate('/bite')} className="back-btn-modern">
            Back to Feed
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="modern-user-profile-fullscreen">
      {/* Header Section - Like Reference Image */}
      <div className="profile-header">
        <div className="header-left">
          <h1 className="bhookad-logo">BHOOKAD</h1>
        </div>
        <div className="header-center">
          <div className="search-container">
            <Search size={18} />
            <input type="text" placeholder="Search" className="search-input" />
          </div>
        </div>
        <div className="header-right">
          <button className="header-icon-btn">
            <MessageCircle size={20} />
          </button>
          <button className="header-icon-btn">
            <Heart size={20} />
          </button>
          <button className="header-icon-btn">
            <Settings size={20} />
          </button>
          <button className="header-icon-btn">
            <User size={20} />
          </button>
        </div>
      </div>

      {/* Main Content Layout - Reference Design */}
      <div className="profile-main-container">
        {/* Left Profile Card */}
        <div className="profile-left-section">
          <div className="profile-card-main">
            {/* Profile Banner with Burger Background */}
            <div className="profile-banner-section">
              <div className="banner-food-bg"></div>
            </div>

            {/* Profile Info */}
            <div className="profile-info-section">
              <div className="profile-avatar-wrapper">
                <img
                  src={profile?.avatar_url || 'https://via.placeholder.com/80'}
                  alt={profile?.name}
                  className="profile-avatar-circle"
                />
              </div>

              <div className="profile-text-info">
                <h2 className="profile-name-text">{profile?.name || 'User'}</h2>
                <p className="profile-handle-text">@{profile?.email?.split('@')[0] || 'user'}</p>
                <p className="profile-bio-text">{profile?.bio || 'Street food enthusiast exploring new flavors daily!'}</p>

                <div className="profile-stats-row">
                  <div className="stat-item-ref">
                    <span className="stat-number-ref">{stats.following}</span>
                    <span className="stat-label-ref">Following</span>
                  </div>
                  <div className="stat-item-ref">
                    <span className="stat-number-ref">{stats.followers}</span>
                    <span className="stat-label-ref">followers</span>
                  </div>
                  <div className="stat-item-ref">
                    <span className="stat-number-ref">{stats.posts}</span>
                    <span className="stat-label-ref">posts</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Center Content Area */}
        <div className="profile-center-section">
          {/* Content Tabs */}
          <div className="content-tabs-ref">
            <button
              className={`tab-btn-ref ${activeTab === 'posts' ? 'active' : ''}`}
              onClick={() => setActiveTab('posts')}
            >
              Food Journey
            </button>
            <button
              className={`tab-btn-ref ${activeTab === 'favorites' ? 'active' : ''}`}
              onClick={() => setActiveTab('favorites')}
            >
              Favorites
            </button>
            <button
              className={`tab-btn-ref ${activeTab === 'following' ? 'active' : ''}`}
              onClick={() => setActiveTab('following')}
            >
              Following
            </button>
          </div>

          {/* Posts Grid */}
          <div className="posts-grid-ref">
            {posts.length > 0 ? (
              posts.map(post => (
                <div key={post.id} className="post-card-ref">
                  <img src={post.image_url} alt="Food post" className="post-image-ref" />
                  <div className="post-info-ref">
                    <h4 className="post-title-ref">{post.content?.split(' ').slice(0, 2).join(' ') || 'Food Post'}</h4>
                    <div className="post-stats-ref">
                      <span className="post-likes-ref">❤️ {post.likes_count || 0}</span>
                      <span className="post-comments-ref">💬 {post.comments_count || 0}</span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="no-posts-ref">
                <div className="no-posts-icon-ref">📸</div>
                <h3>No posts yet</h3>
                <p>Start sharing your food adventures!</p>
              </div>
            )}
          </div>
        </div>

        {/* Right Sidebar */}
        <div className="profile-right-section">
          {/* Food Explorer Section */}
          <div className="sidebar-card-ref">
            <div className="sidebar-header-ref">
              <span className="sidebar-icon-ref">🍽️</span>
              <h3>Food explorer</h3>
            </div>
            <div className="food-types-ref">
              <div className="food-type-ref">
                <span className="food-emoji-ref">🍛</span>
                <span>Indian</span>
              </div>
              <div className="food-type-ref">
                <span className="food-emoji-ref">🥡</span>
                <span>Chinese</span>
              </div>
              <div className="food-type-ref">
                <span className="food-emoji-ref">🍝</span>
                <span>Italian</span>
              </div>
              <div className="food-type-ref">
                <span className="food-emoji-ref">🍕</span>
                <span>Italian</span>
              </div>
            </div>
          </div>

          {/* Top Cuisines Section */}
          <div className="sidebar-card-ref">
            <div className="sidebar-header-ref">
              <span className="sidebar-icon-ref">📍</span>
              <h3>Top cuisines tried</h3>
            </div>
            <div className="cuisine-stats-ref">
              <div className="cuisine-item-ref">
                <span className="cuisine-number-ref">8</span>
                <span>Cities</span>
              </div>
              <div className="cuisine-item-ref">
                <span className="cuisine-number-ref">12</span>
                <span>Fancys</span>
              </div>
            </div>
          </div>

          {/* Taste Preferences Section */}
          <div className="sidebar-card-ref">
            <div className="sidebar-header-ref">
              <span className="sidebar-icon-ref">👅</span>
              <h3>Taste preferences</h3>
            </div>
            <div className="taste-chart-ref">
              <div className="taste-grid-ref"></div>
              <div className="taste-donut-ref"></div>
            </div>
          </div>

          {/* Friends Section */}
          <div className="sidebar-card-ref">
            <div className="sidebar-header-ref">
              <span className="sidebar-icon-ref">👥</span>
              <h3>Friends</h3>
            </div>
            <div className="friends-list-ref">
              <div className="friend-item-ref">
                <img src="https://via.placeholder.com/32" alt="Friend" className="friend-avatar-ref" />
                <div className="friend-info-ref">
                  <span className="friend-name-ref">Arjun</span>
                  <span className="friend-time-ref">1 hour ago</span>
                </div>
                <button className="friend-add-btn-ref">+</button>
              </div>
              <div className="friend-item-ref">
                <img src="https://via.placeholder.com/32" alt="Friend" className="friend-avatar-ref" />
                <div className="friend-info-ref">
                  <span className="friend-name-ref">Sneha</span>
                  <span className="friend-time-ref">2 days ago</span>
                </div>
                <button className="friend-add-btn-ref">+</button>
              </div>
              <div className="friend-item-ref">
                <img src="https://via.placeholder.com/32" alt="Friend" className="friend-avatar-ref" />
                <div className="friend-info-ref">
                  <span className="friend-name-ref">Vikram</span>
                  <span className="friend-time-ref">3 days ago</span>
                </div>
                <button className="friend-add-btn-ref">+</button>
              </div>
            </div>
          </div>

          {/* Recent Activity Section */}
          <div className="sidebar-card-ref">
            <div className="sidebar-header-ref">
              <span className="sidebar-icon-ref">⚡</span>
              <h3>Recent activity</h3>
            </div>
            <div className="activity-placeholder-ref">
              <p>Activity feed coming soon...</p>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Dock Navigation - Same as Feed */}
      <div className="bite-dock-container">
        <Dock
          items={dockItems}
          panelHeight={68}
          baseItemSize={50}
          magnification={70}
          spring={{ mass: 0.03, stiffness: 400, damping: 18 }}
          distance={120}
        />
      </div>
    </div>
  )
}

export default ModernUserProfile

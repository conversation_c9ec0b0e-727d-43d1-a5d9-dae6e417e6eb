import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { MapPin, Calendar, Users, Heart, MessageCircle, Settings, BarChart3, Crown, Home, Search, Plus, Activity } from 'lucide-react'
import FollowButton from '../components/social/FollowButton'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { useFollow } from '../hooks/useFollow'
import './ModernUserProfile.css'

const ModernUserProfile = () => {
  const { userId } = useParams()
  const navigate = useNavigate()
  const { user: currentUser } = useAuth()
  
  const [profile, setProfile] = useState(null)
  const [posts, setPosts] = useState([])
  const [stats, setStats] = useState({ posts: 0, followers: 0, following: 0 })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('posts')

  const { getFollowersCount, getFollowingCount } = useFollow()
  const isOwnProfile = currentUser?.id === userId

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric'
    })
  }

  const fetchProfile = async () => {
    try {
      const profileUserId = userId || currentUser?.id
      console.log('🔄 Fetching profile for userId:', profileUserId)
      setLoading(true)

      if (!profileUserId) {
        throw new Error('No user ID available')
      }

      // Try to fetch real profile first
      const { data: profileData, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', profileUserId)
        .single()

      if (profileError) {
        console.error('❌ Profile error:', profileError)
        console.log('❌ Profile error details:', profileError.message)
        // Create mock profile if user not found
        const mockProfile = {
          id: profileUserId,
          name: `Tarun Soni`,
          role: 'user',
          city: 'Delhi',
          bio: 'Food enthusiast exploring the best flavors in Delhi! 🍕🍜',
          avatar_url: `https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200`,
          created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
          food_preferences: ['Indian', 'Street Food', 'Continental']
        }
        console.log('🔄 Using mock profile:', mockProfile)
        setProfile(mockProfile)
      } else {
        console.log('✅ Real profile found:', profileData)
        setProfile(profileData)
      }

      // Fetch real user posts
      console.log('🔄 Fetching posts for user:', profileUserId)
      const { data: postsData, error: postsError } = await supabase
        .from('posts')
        .select('*')
        .eq('user_id', profileUserId)
        .order('created_at', { ascending: false })

      if (postsError) {
        console.error('❌ Posts error:', postsError)
      }

      if (postsData && postsData.length > 0) {
        console.log('✅ Posts found:', postsData)
        const postsWithCounts = postsData.map(post => ({
          ...post,
          likes_count: Math.floor(Math.random() * 50) + 10, // Mock likes
          comments_count: Math.floor(Math.random() * 20) + 5 // Mock comments
        }))
        setPosts(postsWithCounts)
      } else {
        console.log('❌ No posts found, using empty array')
        setPosts([])
      }

      // Get mock stats for now
      console.log('🔄 Setting stats...')
      const mockStats = {
        posts: postsData?.length || 0,
        followers: 1250,
        following: 340
      }

      console.log('✅ Stats set:', mockStats)
      setStats(mockStats)

    } catch (error) {
      console.error('❌ Error fetching profile:', error)
      setError('Failed to load profile')
    } finally {
      console.log('✅ Setting loading to false')
      setLoading(false)
    }
  }

  const handleFollowChange = () => {
    fetchProfile() // Refresh to update follower count
  }

  useEffect(() => {
    console.log('🚀 Component mounted, userId:', userId)
    console.log('🚀 Current user:', currentUser?.id)

    // Use current user ID if no userId in params
    const profileUserId = userId || currentUser?.id
    console.log('🚀 Profile user ID to fetch:', profileUserId)

    if (profileUserId) {
      fetchProfile()
    } else {
      console.log('❌ No user ID available')
      setError('No user ID provided')
      setLoading(false)
    }
  }, [userId, currentUser])

  // Simple test - always show profile
  const testProfile = {
    id: 'test-123',
    name: 'Test User',
    role: 'user',
    city: 'Delhi',
    bio: 'Food enthusiast exploring the best flavors in the city! 🍕🍜',
    avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200',
    created_at: new Date().toISOString(),
    food_preferences: ['Indian', 'Street Food', 'Continental']
  }

  if (loading) {
    return (
      <div style={{
        background: '#FF6B35',
        color: 'white',
        padding: '2rem',
        textAlign: 'center',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div>
          <h1>🔄 Loading Modern Profile...</h1>
          <p>Profile page is loading...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div style={{
        background: '#FF6B35',
        color: 'white',
        padding: '2rem',
        textAlign: 'center',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div>
          <h1>❌ Profile Error</h1>
          <p>{error}</p>
          <button
            onClick={() => navigate('/bite')}
            style={{
              background: 'white',
              color: '#FF6B35',
              border: 'none',
              padding: '0.75rem 1.5rem',
              borderRadius: '8px',
              fontWeight: '600',
              cursor: 'pointer',
              marginTop: '1rem'
            }}
          >
            Back to Feed
          </button>
        </div>
      </div>
    )
  }

  // Use test profile if no profile loaded
  const displayProfile = profile || testProfile

  return (
    <div className="modern-user-profile-fullscreen">
      {/* Custom Profile Header with Navigation */}
      <div className="profile-nav-header">
        <div className="nav-left">
          <button
            onClick={() => navigate('/bite')}
            className="back-btn-nav"
          >
            ← Back to Feed
          </button>
        </div>
        <div className="nav-center">
          <div className="bhookad-logo">
            <span className="logo-icon">🍽️</span>
            <span className="logo-text">Bhookad</span>
          </div>
        </div>
        <div className="nav-right">
          <button
            onClick={() => navigate('/bite/messages')}
            className="nav-btn"
          >
            💬
          </button>
          <button
            onClick={() => navigate('/bite/activity')}
            className="nav-btn"
          >
            🔔
          </button>
        </div>
      </div>

      <div className="modern-user-profile">
        {/* Orange Food Background Header */}
        <div className="profile-header-modern">
          <div className="food-graphics">
            <div className="food-item burger">🍔</div>
            <div className="food-item taco">🌮</div>
            <div className="food-item soup">🍜</div>
            <div className="food-item pizza">🍕</div>
          </div>
        </div>

        {/* Main Profile Content */}
        <div className="profile-content-modern">
          {/* Left Column - Profile Info */}
          <div className="profile-left">
            <div className="profile-card">
              {/* Avatar and Basic Info */}
              <div className="profile-avatar-section">
                <div className="avatar-container">
                  <img
                    src={displayProfile.avatar_url || 'https://via.placeholder.com/80'}
                    alt={displayProfile.name}
                    className="profile-avatar-modern"
                  />
                  <div className="verification-badge">
                    <span className="role-badge-modern">
                      👤 Food Explorer
                    </span>
                  </div>
                </div>

                <div className="profile-info-modern">
                  <h1 className="profile-name-modern">{displayProfile.name}</h1>
                  <p className="profile-bio-modern">
                    {displayProfile.bio || 'Food enthusiast exploring the best flavors! 🍕🍜'}
                  </p>
                </div>
              </div>

              {/* Stats */}
              <div className="profile-stats-modern">
                <div className="stat-item-modern">
                  <span className="stat-number-modern">{stats.posts}</span>
                  <span className="stat-label-modern">Posts</span>
                </div>
                <div className="stat-divider"></div>
                <div className="stat-item-modern">
                  <span className="stat-number-modern">{stats.followers}</span>
                  <span className="stat-label-modern">Followers</span>
                </div>
                <div className="stat-divider"></div>
                <div className="stat-item-modern">
                  <span className="stat-number-modern">{stats.following}</span>
                  <span className="stat-label-modern">Following</span>
                </div>
              </div>

              {/* Location and Join Date */}
              <div className="profile-meta-modern">
                {profile.city && (
                  <div className="meta-item-modern">
                    <MapPin size={16} />
                    <span>{profile.city}</span>
                  </div>
                )}
                <div className="meta-item-modern">
                  <Calendar size={16} />
                  <span>Joined {formatDate(profile.created_at)}</span>
                </div>
              </div>

              {/* Food Preferences */}
              {profile.food_preferences && profile.food_preferences.length > 0 && (
                <div className="food-preferences-modern">
                  <h4>Food Interests</h4>
                  <div className="preferences-tags-modern">
                    {profile.food_preferences.map((pref, index) => (
                      <span key={index} className="preference-tag-modern">
                        {pref}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="profile-actions-modern">
                {isOwnProfile ? (
                  <>
                    <button className="action-btn-modern primary">
                      <Settings size={18} />
                      Edit Profile
                    </button>
                    <button className="action-btn-modern secondary">
                      <BarChart3 size={18} />
                      Dashboard
                    </button>
                  </>
                ) : (
                  <>
                    <FollowButton
                      targetUserId={userId}
                      targetUserName={profile?.name}
                      size="medium"
                      variant="primary"
                      onFollowChange={handleFollowChange}
                    />
                    <button className="action-btn-modern secondary">
                      <MessageCircle size={18} />
                      Message
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Right Column - Content */}
          <div className="profile-right">
            <div className="content-card">
              {/* Content Tabs */}
              <div className="profile-tabs-modern">
                <button 
                  className={`tab-btn-modern ${activeTab === 'posts' ? 'active' : ''}`}
                  onClick={() => setActiveTab('posts')}
                >
                  Posts
                </button>
                <button 
                  className={`tab-btn-modern ${activeTab === 'liked' ? 'active' : ''}`}
                  onClick={() => setActiveTab('liked')}
                >
                  Liked
                </button>
                <button 
                  className={`tab-btn-modern ${activeTab === 'reviews' ? 'active' : ''}`}
                  onClick={() => setActiveTab('reviews')}
                >
                  Reviews
                </button>
              </div>

              {/* Content Area */}
              <div className="profile-content-area">
                {activeTab === 'posts' && (
                  <div className="posts-grid-modern">
                    {posts.length > 0 ? (
                      posts.map(post => (
                        <div key={post.id} className="profile-post-modern">
                          <img src={post.image_url} alt="Post" />
                          <div className="post-overlay-modern">
                            <div className="post-stats-modern">
                              <span><Heart size={16} /> {post.likes_count}</span>
                              <span><MessageCircle size={16} /> {post.comments_count}</span>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="no-posts-modern">
                        <div className="no-posts-icon">📸</div>
                        <h3>No posts yet</h3>
                        <p>Start sharing your food adventures!</p>
                        {isOwnProfile && (
                          <button 
                            onClick={() => navigate('/bite/create')}
                            className="create-first-post-modern"
                          >
                            Create your first post
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'liked' && (
                  <div className="liked-posts-modern">
                    <div className="coming-soon">
                      <div className="coming-soon-icon">❤️</div>
                      <h3>Liked Posts</h3>
                      <p>Your liked posts will appear here</p>
                    </div>
                  </div>
                )}

                {activeTab === 'reviews' && (
                  <div className="reviews-modern">
                    <div className="coming-soon">
                      <div className="coming-soon-icon">⭐</div>
                      <h3>Reviews</h3>
                      <p>Your restaurant reviews will appear here</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Dock Navigation */}
      <div className="dock-navigation">
        <button
          className="dock-btn"
          onClick={() => navigate('/bite')}
        >
          <Home size={24} />
          <span>Home</span>
        </button>
        <button
          className="dock-btn"
          onClick={() => navigate('/bite/search')}
        >
          <Search size={24} />
          <span>Search</span>
        </button>
        <button
          className="dock-btn create-btn"
          onClick={() => navigate('/bite/create')}
        >
          <Plus size={24} />
          <span>Create</span>
        </button>
        <button
          className="dock-btn"
          onClick={() => navigate('/bite/activity')}
        >
          <Activity size={24} />
          <span>Activity</span>
        </button>
        <button
          className="dock-btn active"
          onClick={() => navigate(`/bite/profile/fc58f787-3690-4b71-b6a7-eb84a8228c10`)}
        >
          <img
            src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=24"
            alt="Profile"
            className="dock-avatar"
          />
          <span>Profile</span>
        </button>
      </div>
    </div>
  )
}

export default ModernUserProfile

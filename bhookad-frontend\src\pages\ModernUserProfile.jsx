import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { MapPin, Calendar, Users, Heart, MessageCircle, Settings, BarChart3, Crown } from 'lucide-react'
import BiteLayout from '../components/social/BiteLayout'
import FollowButton from '../components/social/FollowButton'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { useFollow } from '../hooks/useFollow'
import './ModernUserProfile.css'

const ModernUserProfile = () => {
  const { userId } = useParams()
  const navigate = useNavigate()
  const { user: currentUser } = useAuth()
  
  const [profile, setProfile] = useState(null)
  const [posts, setPosts] = useState([])
  const [stats, setStats] = useState({ posts: 0, followers: 0, following: 0 })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('posts')

  const { getFollowersCount, getFollowingCount } = useFollow()
  const isOwnProfile = currentUser?.id === userId

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric'
    })
  }

  const fetchProfile = async () => {
    try {
      setLoading(true)
      
      // Fetch user profile
      const { data: profileData, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      if (profileError) {
        console.error('Profile error:', profileError)
        // Create mock profile if user not found
        const mockProfile = {
          id: userId,
          name: `Food Explorer ${userId.slice(0, 8)}`,
          role: 'user',
          city: 'Delhi',
          bio: 'Food enthusiast exploring the best flavors in the city! 🍕🍜',
          avatar_url: `https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200`,
          created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
          food_preferences: ['Indian', 'Street Food', 'Continental']
        }
        setProfile(mockProfile)
      } else {
        setProfile(profileData)
      }

      // Fetch user posts
      const { data: postsData } = await supabase
        .from('posts')
        .select(`
          *,
          users!posts_user_id_fkey(name, avatar_url),
          likes:post_likes(count),
          comments:post_comments(count)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (postsData) {
        const postsWithCounts = postsData.map(post => ({
          ...post,
          likes_count: post.likes?.[0]?.count || 0,
          comments_count: post.comments?.[0]?.count || 0
        }))
        setPosts(postsWithCounts)
      }

      // Get stats
      const followersCount = await getFollowersCount(userId)
      const followingCount = await getFollowingCount(userId)
      
      setStats({
        posts: postsData?.length || 0,
        followers: followersCount,
        following: followingCount
      })

    } catch (error) {
      console.error('Error fetching profile:', error)
      setError('Failed to load profile')
    } finally {
      setLoading(false)
    }
  }

  const handleFollowChange = () => {
    fetchProfile() // Refresh to update follower count
  }

  useEffect(() => {
    if (userId) {
      fetchProfile()
    }
  }, [userId])

  if (loading) {
    return (
      <BiteLayout currentPage="profile">
        <div className="profile-loading-modern">
          <div className="loading-spinner-modern"></div>
          <p>Loading profile...</p>
        </div>
      </BiteLayout>
    )
  }

  if (error || !profile) {
    return (
      <BiteLayout currentPage="profile">
        <div className="profile-error-modern">
          <h2>Profile not found</h2>
          <p>{error || 'This user does not exist'}</p>
          <button onClick={() => navigate('/bite')} className="back-btn-modern">
            Back to Feed
          </button>
        </div>
      </BiteLayout>
    )
  }

  return (
    <BiteLayout currentPage="profile">
      <div className="modern-user-profile">
        {/* Orange Food Background Header */}
        <div className="profile-header-modern">
          <div className="food-graphics">
            <div className="food-item burger">🍔</div>
            <div className="food-item taco">🌮</div>
            <div className="food-item soup">🍜</div>
            <div className="food-item pizza">🍕</div>
          </div>
        </div>

        {/* Main Profile Content */}
        <div className="profile-content-modern">
          {/* Left Column - Profile Info */}
          <div className="profile-left">
            <div className="profile-card">
              {/* Avatar and Basic Info */}
              <div className="profile-avatar-section">
                <div className="avatar-container">
                  <img 
                    src={profile.avatar_url || 'https://via.placeholder.com/80'} 
                    alt={profile.name}
                    className="profile-avatar-modern"
                  />
                  <div className="verification-badge">
                    <span className="role-badge-modern">
                      👤 Food Explorer
                    </span>
                  </div>
                </div>
                
                <div className="profile-info-modern">
                  <h1 className="profile-name-modern">{profile.name}</h1>
                  <p className="profile-bio-modern">
                    {profile.bio || 'Food enthusiast exploring the best flavors! 🍕🍜'}
                  </p>
                </div>
              </div>

              {/* Stats */}
              <div className="profile-stats-modern">
                <div className="stat-item-modern">
                  <span className="stat-number-modern">{stats.posts}</span>
                  <span className="stat-label-modern">Posts</span>
                </div>
                <div className="stat-divider"></div>
                <div className="stat-item-modern">
                  <span className="stat-number-modern">{stats.followers}</span>
                  <span className="stat-label-modern">Followers</span>
                </div>
                <div className="stat-divider"></div>
                <div className="stat-item-modern">
                  <span className="stat-number-modern">{stats.following}</span>
                  <span className="stat-label-modern">Following</span>
                </div>
              </div>

              {/* Location and Join Date */}
              <div className="profile-meta-modern">
                {profile.city && (
                  <div className="meta-item-modern">
                    <MapPin size={16} />
                    <span>{profile.city}</span>
                  </div>
                )}
                <div className="meta-item-modern">
                  <Calendar size={16} />
                  <span>Joined {formatDate(profile.created_at)}</span>
                </div>
              </div>

              {/* Food Preferences */}
              {profile.food_preferences && profile.food_preferences.length > 0 && (
                <div className="food-preferences-modern">
                  <h4>Food Interests</h4>
                  <div className="preferences-tags-modern">
                    {profile.food_preferences.map((pref, index) => (
                      <span key={index} className="preference-tag-modern">
                        {pref}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="profile-actions-modern">
                {isOwnProfile ? (
                  <>
                    <button className="action-btn-modern primary">
                      <Settings size={18} />
                      Edit Profile
                    </button>
                    <button className="action-btn-modern secondary">
                      <BarChart3 size={18} />
                      Dashboard
                    </button>
                  </>
                ) : (
                  <>
                    <FollowButton
                      targetUserId={userId}
                      targetUserName={profile?.name}
                      size="medium"
                      variant="primary"
                      onFollowChange={handleFollowChange}
                    />
                    <button className="action-btn-modern secondary">
                      <MessageCircle size={18} />
                      Message
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Right Column - Content */}
          <div className="profile-right">
            <div className="content-card">
              {/* Content Tabs */}
              <div className="profile-tabs-modern">
                <button 
                  className={`tab-btn-modern ${activeTab === 'posts' ? 'active' : ''}`}
                  onClick={() => setActiveTab('posts')}
                >
                  Posts
                </button>
                <button 
                  className={`tab-btn-modern ${activeTab === 'liked' ? 'active' : ''}`}
                  onClick={() => setActiveTab('liked')}
                >
                  Liked
                </button>
                <button 
                  className={`tab-btn-modern ${activeTab === 'reviews' ? 'active' : ''}`}
                  onClick={() => setActiveTab('reviews')}
                >
                  Reviews
                </button>
              </div>

              {/* Content Area */}
              <div className="profile-content-area">
                {activeTab === 'posts' && (
                  <div className="posts-grid-modern">
                    {posts.length > 0 ? (
                      posts.map(post => (
                        <div key={post.id} className="profile-post-modern">
                          <img src={post.image_url} alt="Post" />
                          <div className="post-overlay-modern">
                            <div className="post-stats-modern">
                              <span><Heart size={16} /> {post.likes_count}</span>
                              <span><MessageCircle size={16} /> {post.comments_count}</span>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="no-posts-modern">
                        <div className="no-posts-icon">📸</div>
                        <h3>No posts yet</h3>
                        <p>Start sharing your food adventures!</p>
                        {isOwnProfile && (
                          <button 
                            onClick={() => navigate('/bite/create')}
                            className="create-first-post-modern"
                          >
                            Create your first post
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'liked' && (
                  <div className="liked-posts-modern">
                    <div className="coming-soon">
                      <div className="coming-soon-icon">❤️</div>
                      <h3>Liked Posts</h3>
                      <p>Your liked posts will appear here</p>
                    </div>
                  </div>
                )}

                {activeTab === 'reviews' && (
                  <div className="reviews-modern">
                    <div className="coming-soon">
                      <div className="coming-soon-icon">⭐</div>
                      <h3>Reviews</h3>
                      <p>Your restaurant reviews will appear here</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </BiteLayout>
  )
}

export default ModernUserProfile
